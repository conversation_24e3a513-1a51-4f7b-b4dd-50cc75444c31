import { NextResponse } from 'next/server';

// Simulação de armazenamento (o mesmo do passo anterior)
const signatureStore = new Map(); 

export async function GET(request) {
  const { searchParams } = new URL(request.url);
  const code = searchParams.get('code');
  const state = searchParams.get('state');

  console.log(`[Callback] Recebido. State: ${state}, Code: ${code}`);

  if (!code || !state) {
    return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/?error=auth_failed`);
  }

  // 1. Validar o 'state' para garantir que a requisição é legítima
  if (!signatureStore.has(state)) {
    console.error("[Callback] State inválido ou expirado.");
    return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/?error=invalid_state`);
  }

  const { docHash } = signatureStore.get(state);
  signatureStore.delete(state); // O state deve ser usado apenas uma vez

  try {
    // 2. Trocar o 'code' por um 'access_token' (Servidor-para-Servidor)
    const tokenUrl = 'https://sso.acesso.gov.br/token';
    const redirectUri = `${process.env.NEXT_PUBLIC_APP_URL}/api/govbr/callback`;
    
    const tokenResponse = await fetch(tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code: code,
        redirect_uri: redirectUri,
        client_id: process.env.GOVBR_CLIENT_ID,
        client_secret: process.env.GOVBR_CLIENT_SECRET,
      }),
    });

    if (!tokenResponse.ok) {
      const errorBody = await tokenResponse.json();
      console.error('[Callback] Erro ao obter token:', errorBody);
      throw new Error('Falha ao obter o token de acesso.');
    }

    const { access_token } = await tokenResponse.json();
    console.log('[Callback] Access Token obtido com sucesso.');

    // 3. Chamar nosso próprio endpoint para finalizar a assinatura
    const signApiUrl = `${process.env.NEXT_PUBLIC_APP_URL}/api/govbr/sign-document`;
    const signResponse = await fetch(signApiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ accessToken: access_token, docHash: docHash }),
    });

    if (!signResponse.ok) throw new Error('Falha ao chamar a API de assinatura.');

    const signedResult = await signResponse.json();

    // 4. Redirecionar o usuário para uma página de sucesso com o link para download
    // Em um app real, o 'signedFileUrl' viria da resposta da API gov.br
    const successUrl = `${process.env.NEXT_PUBLIC_APP_URL}/?status=success&file=${signedResult.signedFileUrl}`;
    return NextResponse.redirect(successUrl);

  } catch (error) {
    console.error('[API Callback Error]', error);
    return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/?error=callback_failed`);
  }
}
