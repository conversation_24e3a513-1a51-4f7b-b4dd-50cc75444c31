<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Guia Interativo da Resolução CFP nº 06/2019</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Lora:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes modalIn {
            from { opacity: 0; transform: translateY(-20px) scale(0.95); }
            to { opacity: 1; transform: translateY(0) scale(1); }
        }
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8fafc;
            scroll-behavior: smooth;
        }
        .main-content {
            animation: fadeIn 1s ease-out forwards;
        }
        .accordion-content {
            max-height: 0;
            overflow-y: hidden;
            transition: max-height 0.7s cubic-bezier(0.4, 0, 0.2, 1), padding 0.7s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 0 1.5rem;
        }
        .accordion-item:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.07), 0 4px 6px -4px rgb(0 0 0 / 0.07);
        }
        .accordion-button.open .accordion-icon {
            transform: rotate(135deg);
        }
        .accordion-icon {
            transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .accordion-button:hover {
            background-color: #f8fafc;
        }
        .content-fade-in {
            opacity: 0;
            transform: translateY(-10px);
            transition: opacity 0.5s ease 0.2s, transform 0.5s ease 0.2s;
        }
        .accordion-button.open + .accordion-content .content-fade-in {
            opacity: 1;
            transform: translateY(0);
        }
        .section-icon { width: 2.5rem; height: 2.5rem; padding: 0.5rem; border-radius: 0.5rem; }
        .form-label {
            display: block;
            font-weight: 500;
            color: #475569;
            margin-bottom: 0.25rem;
            font-size: 0.875rem;
        }
        .form-input, .form-textarea {
            width: 100%;
            padding: 0.5rem 0.75rem;
            border: 1px solid #cbd5e1;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            transition: border-color 0.2s, box-shadow 0.2s;
        }
        .form-input:focus, .form-textarea:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
        }
        .form-textarea {
            min-height: 100px;
        }
        .tab-button {
            padding: 0.5rem 1rem;
            border-bottom: 2px solid transparent;
            font-weight: 500;
            color: #64748b;
            transition: color 0.3s, border-color 0.3s;
        }
        .tab-button:hover {
            color: #334155;
        }
        .tab-button.active {
            color: #4f46e5;
            border-color: #4f46e5;
            font-weight: 600;
        }
        .tab-panel {
            display: none;
            padding-bottom: 1rem;
        }
        .tab-panel.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }
        .modal-backdrop {
            background-color: rgba(15, 23, 42, 0.6);
            backdrop-filter: blur(4px);
            transition: opacity 0.3s ease;
        }
        .modal-content {
            animation: modalIn 0.3s ease-out forwards;
            background-color: #f1f5f9;
        }
        .modal-preview {
            background-color: #ffffff;
            font-family: 'Lora', serif;
            white-space: pre-wrap;
            word-wrap: break-word;
            line-height: 1.6;
            color: #1e293b;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
    </style>
    <script src="https://unpkg.com/jspdf@latest/dist/jspdf.umd.min.js"></script>
</head>
<body class="bg-slate-50 text-slate-800">

    <div class="relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-br from-slate-50 via-indigo-50 to-slate-50"></div>
        <main class="relative container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16 md:py-24 main-content">
            <header class="mb-20">
                <div class="grid md:grid-cols-2 gap-12 items-center">
                    <div class="text-left">
                        <h1 class="text-4xl sm:text-5xl md:text-6xl font-extrabold text-slate-900 tracking-tight text-center md:text-left">Guia da Resolução CFP nº 06/2019</h1>
                        <p class="mt-6 text-lg text-slate-600">Um guia interativo e prático para a elaboração de documentos escritos pela(o) psicóloga(o). Navegue pelas seções, entenda os princípios e gere modelos editáveis para a sua prática profissional.</p>
                    </div>
                    <div class="hidden md:block">
                        <svg width="100%" height="100%" viewBox="0 0 300 300" xmlns="http://www.w3.org/2000/svg">
                           <g opacity="0.7">
                                <path d="M 50 50 L 250 50 L 250 250 L 50 250 Z" fill="#eff6ff" stroke="#dbeafe" stroke-width="2"/>
                                <rect x="70" y="70" width="160" height="20" fill="#e0e7ff" rx="5"/>
                                <rect x="70" y="110" width="160" height="10" fill="#e0e7ff" rx="3"/>
                                <rect x="70" y="130" width="160" height="10" fill="#e0e7ff" rx="3"/>
                                <rect x="70" y="150" width="120" height="10" fill="#e0e7ff" rx="3"/>
                                <rect x="70" y="180" width="160" height="10" fill="#e0e7ff" rx="3"/>
                                <rect x="70" y="200" width="100" height="10" fill="#e0e7ff" rx="3"/>
                                <path d="M 200 190 L 220 210 L 200 230" fill="none" stroke="#a5b4fc" stroke-width="3"/>
                           </g>
                        </svg>
                    </div>
                </div>
            </header>
            
            <div id="accordion-container" class="space-y-4">
                <div class="accordion-item bg-white rounded-2xl shadow-md border border-slate-200 overflow-hidden">
                    <button class="accordion-button w-full flex justify-between items-center p-6 text-left">
                        <div class="flex items-center gap-4">
                            <div class="section-icon bg-indigo-100 text-indigo-600"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" /></svg></div>
                            <h2 class="text-2xl font-bold text-slate-900">Seção I: Princípios Fundamentais</h2>
                        </div>
                        <div class="accordion-icon text-3xl text-slate-400 font-light">+</div>
                    </button>
                    <div class="accordion-content">
                        <div class="border-t border-slate-200 space-y-4 content-fade-in px-6 pt-6 pb-8 text-slate-700">
                             <p class="mb-6">Esta seção estabelece a base para toda a comunicação escrita. Um documento psicológico não é apenas um papel, mas um instrumento que reflete o raciocínio técnico, a ética profissional e o compromisso científico da(o) psicóloga(o). É a materialização do serviço prestado.</p>
                             <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="p-4 bg-slate-50 rounded-lg border">
                                    <h4 class="font-semibold text-slate-800">Princípios Técnicos</h4>
                                    <p class="text-sm mt-1">O documento deve ser fidedigno, baseado em dados que validam o pensamento psicológico. Deve considerar o contexto histórico-social do indivíduo, a natureza dinâmica do fenômeno psicológico e ser fundamentado em ciência, ética e legislação.</p>
                                </div>
                                <div class="p-4 bg-slate-50 rounded-lg border">
                                    <h4 class="font-semibold text-slate-800">Princípios da Linguagem</h4>
                                    <p class="text-sm mt-1">Exige-se linguagem impessoal (3ª pessoa), precisa e com nexo causal. O texto deve seguir a norma culta da língua portuguesa, ser objetivo e garantir os direitos humanos. Descrições literais de atendimentos devem ser evitadas, salvo justificativa técnica clara.</p>
                                </div>
                                 <div class="p-4 bg-slate-50 rounded-lg border">
                                    <h4 class="font-semibold text-slate-800">Princípios Éticos</h4>
                                    <p class="text-sm mt-1">O sigilo profissional é central e inegociável. O documento deve ser construído para garantir os direitos humanos, evitando sustentar modelos institucionais de segregação. É dever da(o) psicóloga(o) fornecer o documento sempre que solicitado ou ao final de uma avaliação.</p>
                                </div>
                             </div>
                        </div>
                    </div>
                </div>

                <div class="accordion-item bg-white rounded-2xl shadow-md border border-slate-200 overflow-hidden">
                    <button class="accordion-button w-full flex justify-between items-center p-6 text-left">
                        <div class="flex items-center gap-4">
                            <div class="section-icon bg-blue-100 text-blue-600"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" /></svg></div>
                            <h2 class="text-2xl font-bold text-slate-900">Seções II & III: Modalidades e Estruturas</h2>
                        </div>
                        <div class="accordion-icon text-3xl text-slate-400 font-light">+</div>
                    </button>
                    <div class="accordion-content">
                         <div class="py-6 border-t border-slate-200 space-y-8 content-fade-in px-6 pb-8">
                            <div class="p-4 bg-indigo-50 border-l-4 border-indigo-400 rounded-r-lg space-y-4">
                                <div>
                                    <h4 class="font-bold text-slate-800 mb-2">Informações do Profissional (para todos os modelos)</h4>
                                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                        <div><label for="psi-nome" class="form-label">Nome Completo</label><input type="text" id="psi-nome" placeholder="Seu nome completo ou social" class="form-input"></div>
                                        <div><label for="psi-crp" class="form-label">CRP</label><input type="text" id="psi-crp" placeholder="XX/XXXXX" class="form-input"></div>
                                    </div>
                                </div>
                                <div class="pt-4 border-t border-indigo-200">
                                    <h4 class="font-bold text-slate-800 mb-2">Identidade Visual (Opcional)</h4>
                                    <div class="flex items-center gap-4">
                                        <img id="logo-preview" src="https://placehold.co/100x100/e0e7ff/4338ca?text=Logo" alt="Preview do Logo" class="w-20 h-20 rounded-md object-contain bg-white border-2 border-dashed border-indigo-300 p-1">
                                        <div>
                                            <label for="logo-upload-btn" class="form-label">Logo da Clínica/Consultório</label>
                                            <input type="file" id="logo-upload" class="hidden" accept="image/png, image/jpeg">
                                            <button id="logo-upload-btn" onclick="document.getElementById('logo-upload').click()" class="px-4 py-2 text-sm bg-white text-indigo-700 font-semibold rounded-lg border border-indigo-300 hover:bg-indigo-100">Carregar Imagem</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="tabs-container" class="border-b border-slate-200">
                                <nav class="flex flex-wrap -mb-px">
                                    <button class="tab-button active" data-target="declaracao-panel">Declaração</button>
                                    <button class="tab-button" data-target="atestado-panel">Atestado</button>
                                    <button class="tab-button" data-target="relatorio-panel">Relatório</button>
                                    <button class="tab-button" data-target="laudo-panel">Laudo</button>
                                    <button class="tab-button" data-target="parecer-panel">Parecer</button>
                                </nav>
                            </div>

                            <div id="tab-panels-container">
                                <div id="declaracao-panel" class="tab-panel active">
                                    <h3 class="text-xl font-bold text-slate-800">Declaração</h3>
                                    <p class="text-slate-700 mt-1">Um documento que registra informações objetivas sobre o serviço prestado (ex: comparecimento, horários), sem incluir dados sobre o estado psicológico do atendido.</p>
                                    <div class="mt-4 p-4 bg-slate-100 border border-slate-200 rounded-lg">
                                        <h5 class="font-semibold text-slate-800">Gerador de Modelo - Declaração</h5>
                                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
                                            <div><label for="d-paciente" class="form-label">Nome do Atendido</label><input type="text" id="d-paciente" placeholder="Nome completo ou social" class="form-input"></div>
                                            <div><label for="d-finalidade" class="form-label">Finalidade</label><input type="text" id="d-finalidade" placeholder="Ex: Para fins de comprovação de presença" class="form-input"></div>
                                            <div><label for="d-acompanhante" class="form-label">Acompanhante (Opcional)</label><input type="text" id="d-acompanhante" placeholder="Nome do acompanhante" class="form-input"></div>
                                            <div class="sm:col-span-2"><label for="d-info" class="form-label">Informações sobre o Serviço</label><textarea id="d-info" class="form-textarea" placeholder="Ex: Compareceu ao acompanhamento psicológico no dia XX/XX/XXXX, das XX:XX às XX:XX."></textarea></div>
                                        </div>
                                        <div class="flex gap-4 mt-4">
                                            <button onclick="previewTemplate('declaracao')" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">Pré-visualizar Modelo</button>
                                            <button onclick="showExample('declaracao')" class="bg-slate-300 text-slate-800 px-4 py-2 rounded-md hover:bg-slate-400">Ver Exemplo</button>
                                        </div>
                                    </div>
                                </div>
                                <div id="atestado-panel" class="tab-panel">
                                    <h3 class="text-xl font-bold text-slate-800">Atestado Psicológico</h3>
                                    <p class="text-slate-700 mt-1">Resulta de uma avaliação psicológica e certifica uma determinada situação ou estado psicológico, podendo justificar faltas, aptidão para atividades ou solicitar afastamento.</p>
                                    <div class="mt-4 p-4 bg-slate-100 border border-slate-200 rounded-lg">
                                        <h5 class="font-semibold text-slate-800">Gerador de Modelo - Atestado</h5>
                                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
                                            <div><label for="a-paciente" class="form-label">Nome do Atendido</label><input type="text" id="a-paciente" placeholder="Nome completo ou social" class="form-input"></div>
                                            <div><label for="a-solicitante" class="form-label">Nome do Solicitante</label><input type="text" id="a-solicitante" placeholder="Ex: A própria pessoa, Empresa X" class="form-input"></div>
                                            <div class="sm:col-span-2"><label for="a-finalidade" class="form-label">Finalidade</label><input type="text" id="a-finalidade" placeholder="Ex: Aptidão para o cargo de..., Justificativa de falta" class="form-input"></div>
                                            <div class="sm:col-span-2"><label for="a-condicoes" class="form-label">Descrição das Condições Psicológicas</label><textarea id="a-condicoes" class="form-textarea" placeholder="Atesto que o(a) Sr(a)... apresenta (descrever condições, CID se autorizado)..."></textarea></div>
                                        </div>
                                        <div class="flex gap-4 mt-4">
                                            <button onclick="previewTemplate('atestado')" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">Pré-visualizar Modelo</button>
                                            <button onclick="showExample('atestado')" class="bg-slate-300 text-slate-800 px-4 py-2 rounded-md hover:bg-slate-400">Ver Exemplo</button>
                                        </div>
                                    </div>
                                </div>
                                <div id="relatorio-panel" class="tab-panel">
                                    <h3 class="text-xl font-bold text-slate-800">Relatório Psicológico</h3>
                                    <p class="text-slate-700 mt-1">Comunica a atuação profissional em um processo de trabalho, podendo gerar orientações e encaminhamentos, mas **não** tem como finalidade produzir diagnóstico psicológico.</p>
                                    <div class="mt-4 p-4 bg-slate-100 border border-slate-200 rounded-lg">
                                        <h5 class="font-semibold text-slate-800">Gerador de Estrutura - Relatório</h5>
                                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
                                             <div><label for="r-interessado" class="form-label">Interessado</label><input type="text" id="r-interessado" placeholder="Nome do paciente/grupo/instituição" class="form-input"></div>
                                            <div><label for="r-solicitante" class="form-label">Solicitante</label><input type="text" id="r-solicitante" placeholder="Nome do solicitante" class="form-input"></div>
                                            <div class="sm:col-span-2"><label for="r-finalidade" class="form-label">Finalidade</label><input type="text" id="r-finalidade" class="form-input" placeholder="Ex: Acompanhamento de processo, Encaminhamento..."></div>
                                            <div class="sm:col-span-2"><label for="r-demanda" class="form-label">Descrição da Demanda</label><textarea id="r-demanda" class="form-textarea" placeholder="O que motivou a busca pelo serviço..."></textarea></div>
                                            <div class="sm:col-span-2"><label for="r-procedimento" class="form-label">Procedimento</label><textarea id="r-procedimento" class="form-textarea" placeholder="Raciocínio técnico, recursos utilizados, nº de encontros..."></textarea></div>
                                            <div class="sm:col-span-2"><label for="r-analise" class="form-label">Análise</label><textarea id="r-analise" class="form-textarea" placeholder="Exposição descritiva e analítica do trabalho realizado..."></textarea></div>
                                            <div class="sm:col-span-2"><label for="r-conclusao" class="form-label">Conclusão</label><textarea id="r-conclusao" class="form-textarea" placeholder="Conclusões a partir da análise, encaminhamentos, orientações..."></textarea></div>
                                        </div>
                                        <div class="flex gap-4 mt-4">
                                            <button onclick="previewTemplate('relatorio')" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">Pré-visualizar Estrutura</button>
                                            <button onclick="showExample('relatorio')" class="bg-slate-300 text-slate-800 px-4 py-2 rounded-md hover:bg-slate-400">Ver Exemplo</button>
                                        </div>
                                    </div>
                                </div>
                                 <div id="laudo-panel" class="tab-panel">
                                    <h3 class="text-xl font-bold text-slate-800">Laudo Psicológico</h3>
                                    <p class="text-slate-700 mt-1">É o resultado de um processo de avaliação psicológica, com finalidade de subsidiar decisões. Apresenta informações técnicas e científicas, e a citação de referências é obrigatória.</p>
                                    <div class="mt-4 p-4 bg-slate-100 border border-slate-200 rounded-lg">
                                        <h5 class="font-semibold text-slate-800">Gerador de Estrutura - Laudo</h5>
                                         <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
                                            <div><label for="l-interessado" class="form-label">Interessado</label><input type="text" id="l-interessado" placeholder="Nome do paciente/grupo/instituição" class="form-input"></div>
                                            <div><label for="l-solicitante" class="form-label">Solicitante</label><input type="text" id="l-solicitante" placeholder="Nome do solicitante" class="form-input"></div>
                                            <div class="sm:col-span-2"><label for="l-finalidade" class="form-label">Finalidade</label><input type="text" id="l-finalidade" class="form-input" placeholder="Ex: Subsidiar decisão judicial, Avaliação para cirurgia..."></div>
                                            <div class="sm:col-span-2"><label for="l-demanda" class="form-label">Descrição da Demanda</label><textarea id="l-demanda" class="form-textarea" placeholder="O que motivou a busca pela avaliação psicológica..."></textarea></div>
                                            <div class="sm:col-span-2"><label for="l-procedimento" class="form-label">Procedimento</label><textarea id="l-procedimento" class="form-textarea" placeholder="Instrumentos, testes, entrevistas e outras fontes utilizadas..."></textarea></div>
                                            <div class="sm:col-span-2"><label for="l-analise" class="form-label">Análise</label><textarea id="l-analise" class="form-textarea" placeholder="Análise metódica e objetiva dos dados colhidos e sua integração..."></textarea></div>
                                            <div class="sm:col-span-2"><label for="l-conclusao" class="form-label">Conclusão</label><textarea id="l-conclusao" class="form-textarea" placeholder="Conclusões, diagnóstico/hipótese, prognóstico, encaminhamentos..."></textarea></div>
                                        </div>
                                        <div class="flex gap-4 mt-4">
                                            <button onclick="previewTemplate('laudo')" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">Pré-visualizar Estrutura</button>
                                            <button onclick="showExample('laudo')" class="bg-slate-300 text-slate-800 px-4 py-2 rounded-md hover:bg-slate-400">Ver Exemplo</button>
                                        </div>
                                    </div>
                                </div>
                                <div id="parecer-panel" class="tab-panel">
                                    <h3 class="text-xl font-bold text-slate-800">Parecer Psicológico</h3>
                                    <p class="text-slate-700 mt-1">É um pronunciamento por escrito que responde a uma consulta sobre uma questão-problema ou a um documento psicológico questionado. Não é resultado de uma avaliação ou intervenção direta.</p>
                                    <div class="mt-4 p-4 bg-slate-100 border border-slate-200 rounded-lg">
                                        <h5 class="font-semibold text-slate-800">Gerador de Estrutura - Parecer</h5>
                                         <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
                                            <div><label for="p-solicitante" class="form-label">Solicitante</label><input type="text" id="p-solicitante" placeholder="Nome do solicitante" class="form-input"></div>
                                            <div><label for="p-assunto" class="form-label">Assunto</label><input type="text" id="p-assunto" placeholder="Objeto da consulta" class="form-input"></div>
                                            <div class="sm:col-span-2"><label for="p-demanda" class="form-label">Descrição da Demanda</label><textarea id="p-demanda" class="form-textarea" placeholder="Transcrição do objetivo da consulta..."></textarea></div>
                                            <div class="sm:col-span-2"><label for="p-analise" class="form-label">Análise</label><textarea id="p-analise" class="form-textarea" placeholder="Análise minuciosa da questão com base na teoria e técnica..."></textarea></div>
                                            <div class="sm:col-span-2"><label for="p-conclusao" class="form-label">Conclusão</label><textarea id="p-conclusao" class="form-textarea" placeholder="Posicionamento do parecerista sobre a questão..."></textarea></div>
                                        </div>
                                        <div class="flex gap-4 mt-4">
                                            <button onclick="previewTemplate('parecer')" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">Pré-visualizar Estrutura</button>
                                            <button onclick="showExample('parecer')" class="bg-slate-300 text-slate-800 px-4 py-2 rounded-md hover:bg-slate-400">Ver Exemplo</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                         </div>
                    </div>
                </div>

                <div class="accordion-item bg-white rounded-2xl shadow-md border border-slate-200 overflow-hidden">
                    <button class="accordion-button w-full flex justify-between items-center p-6 text-left">
                         <div class="flex items-center gap-4">
                            <div class="section-icon bg-teal-100 text-teal-600"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" /></svg></div>
                            <h2 class="text-2xl font-bold text-slate-900">Seções IV a VII: Procedimentos Finais</h2>
                        </div>
                        <div class="accordion-icon text-3xl text-slate-400 font-light">+</div>
                    </button>
                    <div class="accordion-content">
                         <div class="py-6 border-t border-slate-200 space-y-6 content-fade-in px-6 text-slate-700 pb-8">
                             <div class="p-4 bg-slate-50 rounded-lg border">
                                <h4 class="font-semibold text-slate-800">IV. Guarda dos Documentos</h4>
                                <p class="text-sm mt-1">Os documentos e os materiais que os fundamentam devem ser guardados pelo prazo mínimo de 5 anos, sendo a guarda responsabilidade da(o) psicóloga(o) e da instituição.</p>
                            </div>
                             <div class="p-4 bg-slate-50 rounded-lg border">
                                <h4 class="font-semibold text-slate-800">V. Destino e Envio</h4>
                                <p class="text-sm mt-1">Os documentos devem ser entregues diretamente ao solicitante ou responsável legal, em entrevista devolutiva, e é obrigatório manter um protocolo de entrega assinado.</p>
                            </div>
                             <div class="p-4 bg-slate-50 rounded-lg border">
                                <h4 class="font-semibold text-slate-800">VI. Prazo de Validade</h4>
                                <p class="text-sm mt-1">O prazo de validade do conteúdo deve ser indicado no último parágrafo do Atestado, Relatório e Laudo Psicológico. A decisão sobre o prazo considera as normas da área e a natureza dinâmica do fenômeno psicológico.</p>
                            </div>
                             <div class="p-4 bg-slate-50 rounded-lg border">
                                <h4 class="font-semibold text-slate-800">VII. Entrevista Devolutiva</h4>
                                <p class="text-sm mt-1">É dever da(o) psicóloga(o) realizar ao menos uma entrevista devolutiva para a entrega de Relatórios e Laudos Psicológicos.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <footer class="mt-20 pt-10 border-t border-slate-300 text-center text-slate-500 text-sm">
                <p class="max-w-3xl mx-auto">Este guia é uma ferramenta de apoio baseada na Resolução CFP nº 06/2019. É fundamental a leitura do documento original para a prática profissional completa e ética.</p>
                <p class="mt-2">
                    <strong>Fonte:</strong> Conselho Federal de Psicologia (CFP).
                    <a href="https://site.cfp.org.br/wp-content/uploads/2019/09/Resolu%C3%A7%C3%A3o-CFP-n-06-2019-comentada.pdf" target="_blank" rel="noopener noreferrer" class="font-semibold text-blue-600 hover:text-blue-800 transition-colors">Acessar a Resolução Comentada</a>.
                </p>
            </footer>
        </main>
    </div>

    <div id="preview-modal" class="fixed inset-0 z-50 flex items-center justify-center p-4 hidden">
        <div class="fixed inset-0 modal-backdrop" onclick="closeModal('preview-modal')"></div>
        <div class="bg-white rounded-lg shadow-2xl p-6 w-full max-w-3xl max-h-[90vh] flex flex-col modal-content">
            <div class="flex justify-between items-center mb-4 pb-4 border-b">
                <h3 class="text-xl font-bold text-slate-900">Pré-visualização do Documento</h3>
                <button onclick="closeModal('preview-modal')" class="text-slate-500 hover:text-slate-800 text-2xl">&times;</button>
            </div>
            <div class="overflow-y-auto flex-grow p-4 rounded-md modal-preview min-h-0">
                <pre id="modal-preview-content"></pre>
            </div>
            
            <input type="file" id="signature-upload" class="hidden" accept="image/png, image/jpeg">

            <div class="mt-6 pt-4 border-t flex flex-col sm:flex-row items-center justify-between gap-4">
                <div class="flex flex-wrap gap-3">
                    <button id="copy-btn" onclick="copyText(this)" class="flex items-center gap-2 px-4 py-2 bg-slate-500 text-white font-semibold rounded-lg shadow-sm hover:bg-slate-600 focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-opacity-75 transition-all transform hover:scale-105">
                        <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75" /></svg>
                        <span class="btn-text">Copiar Texto</span>
                    </button>
                    <button id="signature-btn" onclick="initiateSignatureUpload(this)" class="flex items-center gap-2 px-4 py-2 bg-teal-600 text-white font-semibold rounded-lg shadow-sm hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-opacity-75 transition-all transform hover:scale-105">
                        <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125" /></svg>
                        <span class="btn-text">Anexar Assinatura</span>
                    </button>
                    <button onclick="exportPDF()" class="flex items-center gap-2 px-4 py-2 bg-red-600 text-white font-semibold rounded-lg shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-75 transition-all transform hover:scale-105">
                        <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3" /></svg>
                        <span>Exportar PDF</span>
                    </button>
                </div>
                <button onclick="closeModal('preview-modal')" class="px-4 py-2 bg-transparent text-slate-600 font-semibold rounded-lg hover:bg-slate-200 transition-colors">Fechar</button>
            </div>
        </div>
    </div>
    
    <div id="example-modal" class="fixed inset-0 z-50 flex items-center justify-center p-4 hidden">
        <div class="fixed inset-0 modal-backdrop" onclick="closeModal('example-modal')"></div>
        <div class="bg-white rounded-lg shadow-2xl p-6 w-full max-w-3xl max-h-[90vh] flex flex-col modal-content">
            <div class="flex justify-between items-center mb-4 pb-4 border-b">
                <h3 id="example-modal-title" class="text-xl font-bold text-slate-900">Exemplo de Documento</h3>
                <button onclick="closeModal('example-modal')" class="text-slate-500 hover:text-slate-800 text-2xl">&times;</button>
            </div>
            <div class="overflow-y-auto flex-grow p-4 rounded-md modal-preview min-h-0">
                <pre id="example-modal-content"></pre>
            </div>
            <div class="mt-6 flex justify-end">
                <button onclick="closeModal('example-modal')" class="px-4 py-2 bg-slate-200 text-slate-800 rounded-md hover:bg-slate-300">Fechar</button>
            </div>
        </div>
    </div>

    <script>
        let signatureImage = null;
        let logoImage = null;

        document.addEventListener('DOMContentLoaded', function () {
            // Lógica do Accordion
            const accordionContainer = document.getElementById('accordion-container');
            if (accordionContainer) {
                accordionContainer.addEventListener('click', function(event) {
                    const button = event.target.closest('.accordion-button');
                    if (!button) return;
                    const content = button.nextElementSibling;
                    const isOpening = !button.classList.contains('open');
                    
                    accordionContainer.querySelectorAll('.accordion-item').forEach(item => {
                         if (item !== button.parentElement) {
                            item.querySelector('.accordion-button').classList.remove('open');
                            item.querySelector('.accordion-content').style.maxHeight = '0';
                            item.querySelector('.accordion-content').style.padding = '0 1.5rem';
                        }
                    });
                    
                    if (isOpening) {
                        button.classList.add('open');
                        content.style.padding = '1.5rem';
                        setTimeout(() => {
                           content.style.maxHeight = content.scrollHeight + 'px';
                        }, 50);
                    } else {
                        button.classList.remove('open');
                        content.style.maxHeight = '0';
                        content.style.padding = '0 1.5rem';
                    }
                });
            }

            // Lógica das Abas
            const tabsContainer = document.getElementById('tabs-container');
            const panelsContainer = document.getElementById('tab-panels-container');
            if(tabsContainer && panelsContainer) {
                const tabs = tabsContainer.querySelectorAll('.tab-button');
                const panels = panelsContainer.querySelectorAll('.tab-panel');

                tabs.forEach(tab => {
                    tab.addEventListener('click', () => {
                        tabs.forEach(t => t.classList.remove('active'));
                        tab.classList.add('active');
                        const targetPanelId = tab.dataset.target;
                        panels.forEach(panel => {
                            panel.classList.toggle('active', panel.id === targetPanelId);
                        });
                        const openAccordionContent = document.querySelector('.accordion-button.open + .accordion-content');
                        if(openAccordionContent) {
                            openAccordionContent.style.maxHeight = openAccordionContent.scrollHeight + 'px';
                        }
                    });
                });
            }
            
            // LISTENERS PARA UPLOAD DE IMAGENS
            document.getElementById('signature-upload').addEventListener('change', handleSignatureFile);
            document.getElementById('logo-upload').addEventListener('change', handleLogoFile);
        });
        
        window.closeModal = function(modalId) {
            document.getElementById(modalId).classList.add('hidden');
        }

        // Função para obter os dados do documento em um formato estruturado
        function getDocumentDataObject(type) {
            const date = new Date();
            const data = {
                professionalName: document.getElementById('psi-nome').value || "Nome Completo da(o) Psicóloga(o)",
                professionalCrp: document.getElementById('psi-crp').value || "CRP XX/XXXXX",
                city: "Ribeirão Preto",
                fullDate: `${date.getDate()} de ${date.toLocaleString('pt-BR', { month: 'long' })} de ${date.getFullYear()}`
            };

            switch(type) {
                case 'declaracao':
                    const d_paciente = document.getElementById('d-paciente').value || "[Nome completo ou social]";
                    const d_finalidade = document.getElementById('d-finalidade').value || "[Finalidade]";
                    const d_acompanhante = document.getElementById('d-acompanhante').value;
                    const d_info = document.getElementById('d-info').value || "[Informações sobre o serviço]";
                    let d_acompanhanteText = d_acompanhante ? ` e seu/sua acompanhante, ${d_acompanhante},` : '';
                    
                    data.title = "DECLARAÇÃO";
                    data.body = `Declaro, para fins de ${d_finalidade}, que ${d_paciente}${d_acompanhanteText} esteve presente para a prestação de serviço psicológico.\n\n${d_info}`;
                    break;
                case 'atestado':
                    const a_paciente = document.getElementById('a-paciente').value || "[Nome completo ou social]";
                    const a_solicitante = document.getElementById('a-solicitante').value || "[Solicitante]";
                    const a_finalidade = document.getElementById('a-finalidade').value || "[Finalidade]";
                    const a_condicoes = document.getElementById('a-condicoes').value || "[Descrição das condições psicológicas]";
                    
                    data.title = "ATESTADO PSICOLÓGICO";
                    data.body = `Atesto, a pedido de ${a_solicitante} e para fins de ${a_finalidade}, que o(a) Sr(a). ${a_paciente} foi submetido(a) a avaliação psicológica e apresenta ${a_condicoes}.`;
                    break;
                case 'relatorio':
                     data.title = "RELATÓRIO PSICOLÓGICO";
                     data.body = `1. IDENTIFICAÇÃO\nAutora/Relatora: ${data.professionalName} - ${data.professionalCrp}\nInteressado: ${document.getElementById('r-interessado').value || '[Preencha]'}\nSolicitante: ${document.getElementById('r-solicitante').value || '[Preencha]'}\nFinalidade: ${document.getElementById('r-finalidade').value || '[Preencha]'}\n\n2. DESCRIÇÃO DA DEMANDA\n${document.getElementById('r-demanda').value || '[Preencha]'}\n\n3. PROCEDIMENTO\n${document.getElementById('r-procedimento').value || '[Preencha]'}\n\n4. ANÁLISE\n${document.getElementById('r-analise').value || '[Preencha]'}\n\n5. CONCLUSÃO\n${document.getElementById('r-conclusao').value || '[Preencha]'}`;
                     break;
                case 'laudo':
                    data.title = "LAUDO PSICOLÓGICO";
                    data.body = `1. IDENTIFICAÇÃO\nAutora/Relatora: ${data.professionalName} - ${data.professionalCrp}\nInteressado: ${document.getElementById('l-interessado').value || '[Preencha]'}\nSolicitante: ${document.getElementById('l-solicitante').value || '[Preencha]'}\nFinalidade: ${document.getElementById('l-finalidade').value || '[Preencha]'}\n\n2. DESCRIÇÃO DA DEMANDA\n${document.getElementById('l-demanda').value || '[Preencha]'}\n\n3. PROCEDIMENTO\n${document.getElementById('l-procedimento').value || '[Preencha]'}\n\n4. ANÁLISE\n${document.getElementById('l-analise').value || '[Preencha]'}\n\n5. CONCLUSÃO\n${document.getElementById('l-conclusao').value || '[Preencha]'}\n\n6. REFERÊNCIAS\n[Listar em nota de rodapé as fontes científicas e referências bibliográficas utilizadas.]`;
                    break;
                case 'parecer':
                     data.title = "PARECER PSICOLÓGICO";
                     data.body = `1. IDENTIFICAÇÃO\nParecerista: ${data.professionalName} - ${data.professionalCrp}\nSolicitante: ${document.getElementById('p-solicitante').value || '[Preencha]'}\nAssunto: ${document.getElementById('p-assunto').value || '[Preencha]'}\n\n2. DESCRIÇÃO DA DEMANDA\n${document.getElementById('p-demanda').value || '[Preencha]'}\n\n3. ANÁLISE\n${document.getElementById('p-analise').value || '[Preencha]'}\n\n4. CONCLUSÃO\n${document.getElementById('p-conclusao').value || '[Preencha]'}\n\n5. REFERÊNCIAS\n[Listar em nota de rodapé as fontes científicas e referências bibliográficas utilizadas.]`;
                     break;
            }
            return data;
        }

        function previewTemplate(type) {
            window.currentDocumentType = type;
            const data = getDocumentDataObject(type);

            const previewText = `${data.title}\n\n${data.body}\n\n${data.city}, ${data.fullDate}.\n\n___________________________________\n${data.professionalName}\n${data.professionalCrp}`;
            
            document.getElementById('modal-preview-content').textContent = previewText;
            document.getElementById('preview-modal').classList.remove('hidden');
            
            signatureImage = null; 
            document.querySelector('#copy-btn .btn-text').textContent = 'Copiar Texto';
            document.querySelector('#signature-btn .btn-text').textContent = 'Anexar Assinatura';
        }

        // FUNÇÃO DE EXEMPLOS RESTAURADA
        function showExample(type) {
            const exampleModal = document.getElementById('example-modal');
            const exampleTitle = document.getElementById('example-modal-title');
            const exampleContent = document.getElementById('example-modal-content');
            
            const examples = {
                declaracao: {
                    title: "Exemplo de Declaração",
                    content: `DECLARAÇÃO\n\nDeclaro, para fins de apresentação junto à empresa X, que Maria da Silva (nome social: Maria da Silva) esteve presente em sessão de psicoterapia no dia 11 de setembro de 2025, das 14:00 às 14:50.\n\nSão Paulo, 11 de setembro de 2025.\n\n___________________________________\nDr. João Carlos Pereira\nCRP 06/12345`
                },
                atestado: {
                    title: "Exemplo de Atestado Psicológico",
                    content: `ATESTADO PSICOLÓGICO\n\nAtesto, para fins de apresentação em perícia do INSS, a pedido de Joana Martins, que a referida paciente, após processo de avaliação psicológica, apresenta quadro compatível com Transtorno de Ansiedade Generalizada (CID-10 F41.1), com sintomas significativos de angústia, preocupação excessiva e tensão muscular, que atualmente a incapacitam para o exercício de suas atividades laborais. Sugere-se afastamento do trabalho por um período de 30 (trinta) dias para tratamento.-------------------------------------------------\n\nCuritiba, 11 de setembro de 2025.\n\n___________________________________\nDra. Ana Beatriz Costa\nCRP 08/67890`
                },
                relatorio: {
                    title: "Exemplo de Relatório Psicológico",
                    content: `RELATÓRIO PSICOLÓGICO\n\n1. IDENTIFICAÇÃO\nAutora/Relatora: Dra. Sofia Lima Santos - CRP 05/54321\nInteressado: Carlos Eduardo Oliveira\nSolicitante: Escola Primária Aprender Feliz\nFinalidade: Apresentar informações sobre o acompanhamento psicológico para subsidiar o plano de desenvolvimento individual do aluno.\n\n2. DESCRIÇÃO DA DEMANDA\nO presente relatório foi solicitado pela coordenação pedagógica da escola, com o consentimento dos responsáveis, para compreender as dificuldades de interação social e episódios de agitação motora de Carlos em sala de aula, conforme relatado pela professora.\n\n3. PROCEDIMENTO\nForam realizadas 8 sessões de ludoterapia, com frequência semanal, entre julho e setembro de 2025. Utilizou-se a abordagem da Terapia Cognitivo-Comportamental, com técnicas de psicoeducação emocional, treino de habilidades sociais e observação lúdica. Foram realizadas também 2 sessões de orientação com os pais.\n\n4. ANÁLISE\nDurante as sessões, Carlos demonstrou dificuldade em identificar e nomear emoções como frustração e raiva, reagindo com agitação quando contrariado. Observou-se, no brincar, uma preferência por atividades solitárias e dificuldade em seguir regras em jogos compartilhados. Contudo, mostrou-se receptivo às intervenções de psicoeducação, começando a identificar emoções básicas em si e nos outros. A agitação parece ser um comportamento reativo a situações de sobrecarga social ou frustração não verbalizada.\n\n5. CONCLUSÃO\nConclui-se que as dificuldades de Carlos estão relacionadas a um repertório de habilidades sociais e de regulação emocional ainda em desenvolvimento. Recomenda-se a continuidade do acompanhamento psicológico para fortalecer tais habilidades e sugere-se à escola a implementação de estratégias de mediação em sala, como a criação de um "espaço da calma" e o reforço positivo de comportamentos pró-sociais.\n\nRio de Janeiro, 11 de setembro de 2025.\n\n___________________________________\nDra. Sofia Lima Santos\nCRP 05/54321`
                },
                laudo: {
                     title: "Exemplo de Laudo Psicológico",
                     content: `LAUDO PSICOLÓGICO\n\n1. IDENTIFICAÇÃO\nAutora/Relatora: Dra. Beatriz Almeida - CRP 07/11223\nInteressado: Roberto Menezes\nSolicitante: Dr. Fernando Guimarães, Neurologista\nFinalidade: Avaliação neuropsicológica para investigação de queixas de memória e auxílio em diagnóstico diferencial.\n\n2. DESCRIÇÃO DA DEMANDA\nPaciente de 68 anos, encaminhado pelo neurologista assistente, com queixas de esquecimentos progressivos há cerca de um ano, principalmente para fatos recentes, e dificuldade em encontrar palavras.\n\n3. PROCEDIMENTO\nO processo de avaliação ocorreu em 4 sessões, incluindo entrevista de anamnese com o paciente e sua esposa, e aplicação dos seguintes instrumentos: Mini-Exame do Estado Mental (MEEM), Teste de Aprendizagem Auditivo-Verbal de Rey (RAVLT), Figuras Complexas de Rey, Teste do Relógio, Fluência Verbal (semântica e fonêmica) e Escala de Depressão Geriátrica (GDS-15).\n\n4. ANÁLISE\nOs resultados indicam desempenho cognitivo global abaixo do esperado para a idade e escolaridade (MEEM=22/30). A avaliação da memória evidenciou prejuízo significativo na capacidade de aprendizagem e evocação de novas informações (curva de aprendizagem do RAVLT foi deficitária, com baixa recuperação após intervalo). A memória visual também se mostrou comprometida. Funções executivas e linguagem apresentaram déficits leves, notadamente na evocação lexical (baixa pontuação na fluência verbal). Não foram observados sintomas depressivos clinicamente significativos (GDS=3).\n\n5. CONCLUSÃO\nOs resultados da avaliação neuropsicológica são compatíveis com um perfil de comprometimento cognitivo de múltiplas funções, com predomínio de déficit de memória episódica. A hipótese diagnóstica é de um Transtorno Neurocognitivo Maior, possivelmente devido à Doença de Alzheimer (CID-10 G30). Sugere-se a continuidade da investigação médica e o início de um programa de reabilitação cognitiva para gerenciamento dos déficits no cotidiano.\n\n6. REFERÊNCIAS\n(As referências dos testes seriam listadas aqui, em notas de rodapé no documento final).\n\nPorto Alegre, 11 de setembro de 2025.\n\n___________________________________\nDra. Beatriz Almeida\nCRP 07/11223`
                },
                parecer: {
                    title: "Exemplo de Parecer Psicológico",
                    content: `PARECER PSICOLÓGICO\n\n1. IDENTIFICAÇÃO\nParecerista: Dr. Marcos Vinicius Andrade - CRP 01/99887\nSolicitante: Dr. Advogado José da Costa\nAssunto: Análise técnica do Laudo Psicológico datado de 10/08/2025, presente nos autos do processo nº 12345-67.2025.\n\n2. DESCRIÇÃO DA DEMANDA\nFoi solicitado um parecer técnico sobre a consistência metodológica, técnica e ética do laudo psicológico elaborado pela psicóloga perita no processo de disputa de guarda, que avaliou as condições psicológicas dos genitores.\n\n3. ANÁLISE\nO laudo em questão apresenta uma estrutura adequada, conforme a Resolução CFP nº 06/2019. Contudo, na seção "Procedimento", a perita cita a aplicação de instrumentos restritos à(ao) psicóloga(o) (ex: Teste HTP) mas não descreve os resultados de forma clara na seção "Análise", baseando suas conclusões majoritariamente em observações clínicas não sistematizadas. A análise foca excessivamente em descrições comportamentais da genitora, sem a devida articulação com os construtos psicológicos avaliados. A conclusão apresenta afirmações conclusivas sobre a alienação parental que não parecem ser suficientemente sustentadas pelos dados apresentados na análise, carecendo de nexo causal robusto.\n\n4. CONCLUSÃO\nEste parecerista conclui que o laudo psicológico analisado apresenta fragilidades técnico-científicas. Embora a estrutura formal seja adequada, a falta de integração entre os dados dos instrumentos e a análise, bem como as conclusões que extrapolam os resultados apresentados, comprometem a sua consistência. Recomenda-se que o juízo considere a possibilidade de solicitar esclarecimentos à perita ou a realização de uma nova avaliação para subsidiar a decisão de forma mais segura.\n\n5. REFERÊNCIAS\n(Referências sobre avaliação psicológica forense e uso de testes seriam listadas aqui).\n\nBrasília, 11 de setembro de 2025.\n\n___________________________________\nDr. Marcos Vinicius Andrade\nCRP 01/99887`
                }
            };

            exampleTitle.textContent = examples[type].title;
            exampleContent.textContent = examples[type].content;
            exampleModal.classList.remove('hidden');
        }

        function copyText(buttonElement) {
            const textToCopy = document.getElementById('modal-preview-content').textContent;
            navigator.clipboard.writeText(textToCopy).then(() => {
                const textSpan = buttonElement.querySelector('.btn-text');
                const originalText = textSpan.textContent;
                textSpan.textContent = 'Copiado!';
                setTimeout(() => { textSpan.textContent = originalText; }, 2000);
            }).catch(err => {
                console.error('Erro ao copiar o texto: ', err);
            });
        }

        function initiateSignatureUpload() {
            document.getElementById('signature-upload').click();
        }

        function handleSignatureFile(event) {
            const file = event.target.files[0];
            const textSpan = document.querySelector('#signature-btn .btn-text');
            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    signatureImage = e.target.result;
                    textSpan.textContent = 'Assinatura Anexada!';
                };
                reader.readAsDataURL(file);
            }
        }

        function handleLogoFile(event) {
            const file = event.target.files[0];
            const preview = document.getElementById('logo-preview');
            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    logoImage = e.target.result;
                    preview.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        }
        
        function exportPDF() {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF({ orientation: 'p', unit: 'mm', format: 'a4' });
            
            const data = getDocumentDataObject(window.currentDocumentType);
            const pageWidth = doc.internal.pageSize.getWidth();
            const margin = 20;
            const usableWidth = pageWidth - (margin * 2);
            let yPos = 20;

            if (logoImage) {
                try {
                    const imgProps = doc.getImageProperties(logoImage);
                    const logoHeight = 25;
                    const logoWidth = (imgProps.width * logoHeight) / imgProps.height;
                    const logoX = (pageWidth / 2) - (logoWidth / 2);
                    doc.addImage(logoImage, 'PNG', logoX, yPos, logoWidth, logoHeight);
                    yPos += logoHeight + 15;
                } catch (e) { console.error("Erro ao adicionar o logo: ", e); }
            } else {
                yPos += 15;
            }

            doc.setFont('Times-Roman', 'bold');
            doc.setFontSize(16);
            doc.text(data.title, pageWidth / 2, yPos, { align: 'center' });
            yPos += 20;

            doc.setFont('Times-Roman', 'normal');
            doc.setFontSize(12);
            const bodyLines = doc.splitTextToSize(data.body, usableWidth);
            doc.text(bodyLines, margin, yPos);
            yPos += (bodyLines.length * 5) + 20;

            const dateText = `${data.city}, ${data.fullDate}.`;
            doc.text(dateText, pageWidth - margin, yPos, { align: 'right' });
            yPos += 30;

            if (signatureImage) {
                try {
                    const sigProps = doc.getImageProperties(signatureImage);
                    const sigHeight = 25;
                    const sigWidth = (sigProps.width * sigHeight) / sigProps.height;
                    const sigX = (pageWidth / 2) - (sigWidth / 2);
                    doc.addImage(signatureImage, 'PNG', sigX, yPos - 5, sigWidth, sigHeight);
                    yPos += sigHeight;
                } catch (e) { console.error("Erro ao adicionar a assinatura: ", e); }
            }

            const lineXStart = (pageWidth / 2) - 40;
            doc.line(lineXStart, yPos, lineXStart + 80, yPos);
            yPos += 5;
            doc.text(data.professionalName, pageWidth / 2, yPos, { align: 'center' });
            yPos += 5;
            doc.text(data.professionalCrp, pageWidth / 2, yPos, { align: 'center' });

            doc.save(`${data.title.toLowerCase().replace(/ /g, '_')}.pdf`);
        }
    </script>
</body>
</html>