'use client';

import React, { useState, useEffect, useRef } from 'react';

// --- <PERSON><PERSON><PERSON> como Componentes React ---

const MenuIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-full h-full">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
    </svg>
);

const DocumentIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-full h-full">
        <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
    </svg>
);

const ClockIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-full h-full">
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
    </svg>
);

const CopyIcon = () => (
    <svg className="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75" />
    </svg>
);

const GovBrIcon = () => (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1.03 15.44l-3.5-3.5 1.41-1.41 2.09 2.09 4.59-4.59L18.41 9.5l-6.03 5.94z" />
    </svg>
);


const DownloadIcon = () => (
    <svg className="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3" />
    </svg>
);

// --- Componente principal da Aplicação ---
export default function GuiaDocumentosPsicologicos() {
    // --- State Hooks ---
    const [openAccordion, setOpenAccordion] = useState(null);
    const [activeTab, setActiveTab] = useState('declaracao');
    const [modal, setModal] = useState({ isOpen: false, type: '', content: '', title: '' });
    
    const [professionalInfo, setProfessionalInfo] = useState({ nome: '', crp: '' });
    const [logoImage, setLogoImage] = useState(null);
    const [currentDocumentForSigning, setCurrentDocumentForSigning] = useState<{ type: string; data: any } | null>(null);
    const [signingStatus, setSigningStatus] = useState('idle'); // idle, generating, redirecting, signing, success, error

    // Estado para os campos de cada tipo de documento
    const [formState, setFormState] = useState({
        declaracao: { paciente: '', finalidade: '', acompanhante: '', info: '' },
        atestado: { paciente: '', solicitante: '', finalidade: '', condicoes: '' },
        relatorio: { interessado: '', solicitante: '', finalidade: '', demanda: '', procedimento: '', analise: '', conclusao: '' },
        laudo: { interessado: '', solicitante: '', finalidade: '', demanda: '', procedimento: '', analise: '', conclusao: '' },
        parecer: { solicitante: '', assunto: '', demanda: '', analise: '', conclusao: '' },
    });
    
    // Refs para os inputs de arquivo
    const logoUploadRef = useRef(null);
    
    // --- Effect para carregar a biblioteca jsPDF do CDN ---
    useEffect(() => {
        const script = document.createElement('script');
        script.src = 'https://unpkg.com/jspdf@latest/dist/jspdf.umd.min.js';
        script.async = true;
        document.body.appendChild(script);

        return () => {
            document.body.removeChild(script);
        };
    }, []);

    // --- Handlers ---
    const handleAccordionToggle = (index) => {
        setOpenAccordion(openAccordion === index ? null : index);
    };
    
    const handleInputChange = (e) => {
        const { value, dataset } = e.target;
        const { type, field } = dataset;

        if (type === 'professional') {
            setProfessionalInfo(prev => ({ ...prev, [field]: value }));
        } else {
            setFormState(prev => ({
                ...prev,
                [type]: { ...prev[type], [field]: value }
            }));
        }
    };
    
    const handleFileChange = (e, setter) => {
        const file = e.target.files[0];
        if (file && file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = (event) => {
                setter(event.target.result);
            };
            reader.readAsDataURL(file);
        }
    };

    const getDocumentDataObject = (type) => {
        const date = new Date();
        const data = {
            professionalName: professionalInfo.nome || "Nome Completo da(o) Psicóloga(o)",
            professionalCrp: professionalInfo.crp || "CRP XX/XXXXX",
            city: "Ribeirão Preto",
            fullDate: `${date.getDate()} de ${date.toLocaleString('pt-BR', { month: 'long' })} de ${date.getFullYear()}`
        };
        const formData = formState[type];

        switch(type) {
            case 'declaracao':
                const d_acompanhanteText = formData.acompanhante ? ` e seu/sua acompanhante, ${formData.acompanhante},` : '';
                data.title = "DECLARAÇÃO";
                data.body = `Declaro, para fins de ${formData.finalidade || '[Finalidade]'}, que ${formData.paciente || '[Nome completo ou social]'}${d_acompanhanteText} esteve presente para a prestação de serviço psicológico.\n\n${formData.info || '[Informações sobre o serviço]'}`;
                break;
            case 'atestado':
                data.title = "ATESTADO PSICOLÓGICO";
                data.body = `Atesto, a pedido de ${formData.solicitante || '[Solicitante]'} e para fins de ${formData.finalidade || '[Finalidade]'}, que o(a) Sr(a). ${formData.paciente || '[Nome completo ou social]'} foi submetido(a) a avaliação psicológica e apresenta ${formData.condicoes || '[Descrição das condições psicológicas]'}.`;
                break;
            case 'relatorio':
                 data.title = "RELATÓRIO PSICOLÓGICO";
                 data.body = `1. IDENTIFICAÇÃO\nAutora/Relatora: ${data.professionalName} - ${data.professionalCrp}\nInteressado: ${formData.interessado || '[Preencha]'}\nSolicitante: ${formData.solicitante || '[Preencha]'}\nFinalidade: ${formData.finalidade || '[Preencha]'}\n\n2. DESCRIÇÃO DA DEMANDA\n${formData.demanda || '[Preencha]'}\n\n3. PROCEDIMENTO\n${formData.procedimento || '[Preencha]'}\n\n4. ANÁLISE\n${formData.analise || '[Preencha]'}\n\n5. CONCLUSÃO\n${formData.conclusao || '[Preencha]'}`;
                 break;
            case 'laudo':
                data.title = "LAUDO PSICOLÓGICO";
                data.body = `1. IDENTIFICAÇÃO\nAutora/Relatora: ${data.professionalName} - ${data.professionalCrp}\nInteressado: ${formData.interessado || '[Preencha]'}\nSolicitante: ${formData.solicitante || '[Preencha]'}\nFinalidade: ${formData.finalidade || '[Preencha]'}\n\n2. DESCRIÇÃO DA DEMANDA\n${formData.demanda || '[Preencha]'}\n\n3. PROCEDIMENTO\n${formData.procedimento || '[Preencha]'}\n\n4. ANÁLISE\n${formData.analise || '[Preencha]'}\n\n5. CONCLUSÃO\n${formData.conclusao || '[Preencha]'}\n\n6. REFERÊNCIAS\n[Listar em nota de rodapé as fontes científicas e referências bibliográficas utilizadas.]`;
                break;
            case 'parecer':
                 data.title = "PARECER PSICOLÓGICO";
                 data.body = `1. IDENTIFICAÇÃO\nParecerista: ${data.professionalName} - ${data.professionalCrp}\nSolicitante: ${formData.solicitante || '[Preencha]'}\nAssunto: ${formData.assunto || '[Preencha]'}\n\n2. DESCRIÇÃO DA DEMANDA\n${formData.demanda || '[Preencha]'}\n\n3. ANÁLISE\n${formData.analise || '[Preencha]'}\n\n4. CONCLUSÃO\n${formData.conclusao || '[Preencha]'}\n\n5. REFERÊNCIAS\n[Listar em nota de rodapé as fontes científicas e referências bibliográficas utilizadas.]`;
                 break;
            default:
                 data.title = "DOCUMENTO";
                 data.body = "Selecione um tipo de documento válido.";
        }
        return data;
    };

    const previewTemplate = (type) => {
        const data = getDocumentDataObject(type);
        const previewText = `${data.title}\n\n${data.body}\n\n${data.city}, ${data.fullDate}.\n\n___________________________________\n${data.professionalName}\n${data.professionalCrp}`;
        setCurrentDocumentForSigning({ type, data });
        setModal({ isOpen: true, type: 'preview', content: previewText, title: 'Pré-visualização do Documento' });
        setSigningStatus('idle');
    };

    const showExample = (type: string) => {
        const examples: Record<string, { title: string; content: string }> = {
            declaracao: {
                title: "Exemplo - Declaração",
                content: `DECLARAÇÃO

Declaro, para fins de comprovação de atendimento psicológico, que Maria Silva esteve presente para a prestação de serviço psicológico.

O atendimento foi realizado em sessão individual de psicoterapia, com duração de 50 minutos, conforme agendamento prévio.

Ribeirão Preto, 15 de março de 2024.

___________________________________
Dr. João Santos
CRP 06/12345`
            },
            atestado: {
                title: "Exemplo - Atestado Psicológico",
                content: `ATESTADO PSICOLÓGICO

Atesto, a pedido do próprio interessado e para fins de apresentação em processo seletivo, que o(a) Sr(a). João Silva foi submetido(a) a avaliação psicológica e apresenta condições psicológicas adequadas para o exercício de atividades que exigem concentração e trabalho em equipe.

Ribeirão Preto, 15 de março de 2024.

___________________________________
Dr. João Santos
CRP 06/12345`
            },
            relatorio: {
                title: "Exemplo - Relatório Psicológico",
                content: `RELATÓRIO PSICOLÓGICO

1. IDENTIFICAÇÃO
Autora/Relatora: Dr. João Santos - CRP 06/12345
Interessado: Maria Silva
Solicitante: Tribunal de Justiça
Finalidade: Avaliação para processo de guarda

2. DESCRIÇÃO DA DEMANDA
Avaliação psicológica solicitada pelo Tribunal de Justiça para subsidiar decisão judicial em processo de guarda de menor.

3. PROCEDIMENTO
Foram realizadas 3 sessões de avaliação psicológica, incluindo entrevista clínica, aplicação de testes psicológicos e observação comportamental.

4. ANÁLISE
A avaliada apresenta adequado funcionamento psicológico, com recursos emocionais preservados e capacidade para exercer a função parental.

5. CONCLUSÃO
Com base nos dados coletados, conclui-se que a avaliada possui condições psicológicas adequadas para o exercício da guarda do menor.`
            },
            laudo: {
                title: "Exemplo - Laudo Psicológico",
                content: `LAUDO PSICOLÓGICO

1. IDENTIFICAÇÃO
Autora/Relatora: Dr. João Santos - CRP 06/12345
Interessado: Pedro Silva
Solicitante: INSS
Finalidade: Perícia para benefício previdenciário

2. DESCRIÇÃO DA DEMANDA
Avaliação psicológica para verificar incapacidade laborativa por transtorno mental.

3. PROCEDIMENTO
Avaliação realizada através de entrevista clínica estruturada, aplicação de instrumentos psicológicos validados e análise documental.

4. ANÁLISE
O avaliado apresenta sintomas compatíveis com episódio depressivo maior, com prejuízo significativo no funcionamento ocupacional e social.

5. CONCLUSÃO
Conclui-se pela presença de incapacidade laborativa temporária, com necessidade de acompanhamento psicológico especializado.

6. REFERÊNCIAS
[Listar em nota de rodapé as fontes científicas e referências bibliográficas utilizadas.]`
            },
            parecer: {
                title: "Exemplo - Parecer Psicológico",
                content: `PARECER PSICOLÓGICO

1. IDENTIFICAÇÃO
Parecerista: Dr. João Santos - CRP 06/12345
Solicitante: Conselho Regional de Psicologia
Assunto: Análise de protocolo de atendimento

2. DESCRIÇÃO DA DEMANDA
Análise técnica sobre protocolo de atendimento psicológico em situação de emergência.

3. ANÁLISE
O protocolo apresentado está em conformidade com as diretrizes técnicas e éticas da profissão, contemplando os aspectos fundamentais do atendimento psicológico emergencial.

4. CONCLUSÃO
O protocolo analisado atende aos requisitos técnicos e pode ser implementado conforme proposto.

5. REFERÊNCIAS
[Listar em nota de rodapé as fontes científicas e referências bibliográficas utilizadas.]`
            }
        };
        setModal({ isOpen: true, type: 'example', content: examples[type]?.content || "Exemplo não disponível.", title: examples[type]?.title || "Exemplo" });
    };

    const copyText = (buttonElement: HTMLElement, textToCopy: string) => {
        navigator.clipboard.writeText(textToCopy).then(() => {
            buttonElement.textContent = 'Copiado!';
            setTimeout(() => {
                buttonElement.innerHTML = '<svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75" /></svg> <span>Copiar Texto</span>';
            }, 2000);
        }).catch(err => console.error('Erro ao copiar o texto: ', err));
    };

    const generatePdfBlob = (docData: any) => {
        if (!(window as any).jspdf) {
            console.error("jsPDF not loaded yet");
            alert("A biblioteca de PDF ainda não foi carregada. Tente novamente em alguns segundos.");
            return null;
        }
        const { jsPDF } = (window as any).jspdf;
        const doc = new jsPDF({ orientation: 'p', unit: 'mm', format: 'a4' });
        // ... (código de geração de PDF, sem alterações)
        const pageWidth = doc.internal.pageSize.getWidth();
        const margin = 20;
        const usableWidth = pageWidth - (margin * 2);
        let yPos = 20;
        if (logoImage) {
            const imgProps = doc.getImageProperties(logoImage);
            const logoHeight = 25;
            const logoWidth = (imgProps.width * logoHeight) / imgProps.height;
            const logoX = (pageWidth / 2) - (logoWidth / 2);
            doc.addImage(logoImage, 'PNG', logoX, yPos, logoWidth, logoHeight);
            yPos += logoHeight + 15;
        } else {
            yPos += 15;
        }
        doc.setFont('Times-Roman', 'bold');
        doc.setFontSize(16);
        doc.text(docData.title, pageWidth / 2, yPos, { align: 'center' });
        yPos += 20;
        doc.setFont('Times-Roman', 'normal');
        doc.setFontSize(12);
        const bodyLines = doc.splitTextToSize(docData.body, usableWidth);
        doc.text(bodyLines, margin, yPos);
        yPos += (bodyLines.length * 5) + 20;
        doc.text(`${docData.city}, ${docData.fullDate}.`, pageWidth - margin, yPos, { align: 'right' });
        yPos += 30;
        const lineXStart = (pageWidth / 2) - 40;
        doc.line(lineXStart, yPos, lineXStart + 80, yPos);
        yPos += 5;
        doc.text(docData.professionalName, pageWidth / 2, yPos, { align: 'center' });
        yPos += 5;
        doc.text(docData.professionalCrp, pageWidth / 2, yPos, { align: 'center' });
        return doc.output('blob');
    };
    
    const exportPDF = () => {
        if (!currentDocumentForSigning) {
            alert("Gere uma pré-visualização do documento primeiro.");
            return;
        }

        const pdfBlob = generatePdfBlob(currentDocumentForSigning.data);
        if (!pdfBlob) return;

        const url = URL.createObjectURL(pdfBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${currentDocumentForSigning.data.title.toLowerCase().replace(/ /g, '_')}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    };

    const handleGovBrSignature = async () => {
        if (!currentDocumentForSigning) {
            alert("Gere uma pré-visualização do documento primeiro.");
            return;
        }
        setSigningStatus('generating');

        const pdfBlob = generatePdfBlob(currentDocumentForSigning.data);
        if (!pdfBlob) {
            setSigningStatus('error');
            return;
        }

        const pdfFile = new File([pdfBlob], `${currentDocumentForSigning.data.title.toLowerCase().replace(/ /g, '_')}.pdf`, { type: 'application/pdf' });
        
        const formData = new FormData();
        formData.append('file', pdfFile);

        try {
            setSigningStatus('redirecting');
            const response = await fetch('/api/govbr/initiate-signature', {
                method: 'POST',
                body: formData,
            });

            if (!response.ok) throw new Error('Falha ao iniciar o processo no backend.');

            const { authorizationUrl } = await response.json();
            
            // Redireciona o usuário para o gov.br
            window.location.href = authorizationUrl;

        } catch (error) {
            console.error("Erro ao contatar o backend:", error);
            setSigningStatus('error');
            alert("Ocorreu um erro ao tentar iniciar a assinatura. Verifique o console.");
        }
    };
    
    // ... (resto do componente, renderização, etc., sem alterações)

    // O JSX que será renderizado
    return (
        <>
            <style>{`
                @keyframes modalIn {
                    from {
                        opacity: 0;
                        transform: scale(0.95);
                    }
                    to {
                        opacity: 1;
                        transform: scale(1);
                    }
                }

                .main-content {
                    min-height: 100vh;
                }

                /* Custom scrollbar for modal */
                .overflow-y-auto::-webkit-scrollbar {
                    width: 8px;
                }

                .overflow-y-auto::-webkit-scrollbar-track {
                    background: #f1f5f9;
                    border-radius: 4px;
                }

                .overflow-y-auto::-webkit-scrollbar-thumb {
                    background: #cbd5e1;
                    border-radius: 4px;
                }

                .overflow-y-auto::-webkit-scrollbar-thumb:hover {
                    background: #94a3b8;
                }

                /* Responsive text wrapping */
                .word-wrap {
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                }
            `}</style>
            
            <div className="relative overflow-hidden bg-slate-50 text-slate-800">
                <main className="relative container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16 md:py-24 main-content">
                    {/* Header */}
                    <div className="text-center mb-16">
                        <h1 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
                            Guia de Documentos Psicológicos
                        </h1>
                        <p className="text-xl text-slate-600 max-w-3xl mx-auto">
                            Ferramenta completa para criação de documentos psicológicos conforme as diretrizes do CFP
                        </p>
                    </div>

                    {/* Professional Info Section */}
                    <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
                        <h2 className="text-2xl font-bold text-slate-900 mb-4">Informações Profissionais</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-slate-700 mb-2">Nome Completo</label>
                                <input
                                    type="text"
                                    data-type="professional"
                                    data-field="nome"
                                    value={professionalInfo.nome}
                                    onChange={handleInputChange}
                                    className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="Seu nome completo"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-slate-700 mb-2">CRP</label>
                                <input
                                    type="text"
                                    data-type="professional"
                                    data-field="crp"
                                    value={professionalInfo.crp}
                                    onChange={handleInputChange}
                                    className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="CRP XX/XXXXX"
                                />
                            </div>
                        </div>
                        <div className="mt-4">
                            <label className="block text-sm font-medium text-slate-700 mb-2">Logo (opcional)</label>
                            <input
                                ref={logoUploadRef}
                                type="file"
                                accept="image/*"
                                onChange={(e) => handleFileChange(e, setLogoImage)}
                                className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                    </div>

                    {/* Document Types Tabs */}
                    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
                        <div className="border-b border-slate-200">
                            <nav className="flex space-x-8 px-6" aria-label="Tabs">
                                {[
                                    { id: 'declaracao', name: 'Declaração' },
                                    { id: 'atestado', name: 'Atestado' },
                                    { id: 'relatorio', name: 'Relatório' },
                                    { id: 'laudo', name: 'Laudo' },
                                    { id: 'parecer', name: 'Parecer' }
                                ].map((tab) => (
                                    <button
                                        key={tab.id}
                                        onClick={() => setActiveTab(tab.id)}
                                        className={`py-4 px-1 border-b-2 font-medium text-sm ${
                                            activeTab === tab.id
                                                ? 'border-blue-500 text-blue-600'
                                                : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
                                        }`}
                                    >
                                        {tab.name}
                                    </button>
                                ))}
                            </nav>
                        </div>

                        {/* Tab Content */}
                        <div className="p-6">
                            {/* Declaração Form */}
                            {activeTab === 'declaracao' && (
                                <div className="space-y-4">
                                    <h3 className="text-lg font-semibold text-slate-900">Declaração</h3>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-slate-700 mb-2">Nome do Paciente</label>
                                            <input
                                                type="text"
                                                data-type="declaracao"
                                                data-field="paciente"
                                                value={formState.declaracao.paciente}
                                                onChange={handleInputChange}
                                                className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                placeholder="Nome completo ou social"
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-slate-700 mb-2">Finalidade</label>
                                            <input
                                                type="text"
                                                data-type="declaracao"
                                                data-field="finalidade"
                                                value={formState.declaracao.finalidade}
                                                onChange={handleInputChange}
                                                className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                placeholder="Ex: comprovação de atendimento"
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-slate-700 mb-2">Acompanhante (opcional)</label>
                                            <input
                                                type="text"
                                                data-type="declaracao"
                                                data-field="acompanhante"
                                                value={formState.declaracao.acompanhante}
                                                onChange={handleInputChange}
                                                className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                placeholder="Nome do acompanhante"
                                            />
                                        </div>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-slate-700 mb-2">Informações sobre o serviço</label>
                                        <textarea
                                            data-type="declaracao"
                                            data-field="info"
                                            value={formState.declaracao.info}
                                            onChange={handleInputChange}
                                            rows={3}
                                            className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Descreva o tipo de atendimento realizado"
                                        />
                                    </div>
                                    <div className="flex gap-3">
                                        <button
                                            onClick={() => previewTemplate('declaracao')}
                                            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                                        >
                                            Gerar Pré-visualização
                                        </button>
                                        <button
                                            onClick={() => showExample('declaracao')}
                                            className="px-4 py-2 bg-slate-500 text-white rounded-lg hover:bg-slate-600"
                                        >
                                            Ver Exemplo
                                        </button>
                                    </div>
                                </div>
                            )}

                            {/* Atestado Form */}
                            {activeTab === 'atestado' && (
                                <div className="space-y-4">
                                    <h3 className="text-lg font-semibold text-slate-900">Atestado Psicológico</h3>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-slate-700 mb-2">Nome do Paciente</label>
                                            <input
                                                type="text"
                                                data-type="atestado"
                                                data-field="paciente"
                                                value={formState.atestado.paciente}
                                                onChange={handleInputChange}
                                                className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                placeholder="Nome completo ou social"
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-slate-700 mb-2">Solicitante</label>
                                            <input
                                                type="text"
                                                data-type="atestado"
                                                data-field="solicitante"
                                                value={formState.atestado.solicitante}
                                                onChange={handleInputChange}
                                                className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                placeholder="Quem está solicitando"
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-slate-700 mb-2">Finalidade</label>
                                            <input
                                                type="text"
                                                data-type="atestado"
                                                data-field="finalidade"
                                                value={formState.atestado.finalidade}
                                                onChange={handleInputChange}
                                                className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                placeholder="Ex: processo seletivo"
                                            />
                                        </div>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-slate-700 mb-2">Condições Psicológicas</label>
                                        <textarea
                                            data-type="atestado"
                                            data-field="condicoes"
                                            value={formState.atestado.condicoes}
                                            onChange={handleInputChange}
                                            rows={3}
                                            className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Descreva as condições psicológicas observadas"
                                        />
                                    </div>
                                    <div className="flex gap-3">
                                        <button
                                            onClick={() => previewTemplate('atestado')}
                                            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                                        >
                                            Gerar Pré-visualização
                                        </button>
                                        <button
                                            onClick={() => showExample('atestado')}
                                            className="px-4 py-2 bg-slate-500 text-white rounded-lg hover:bg-slate-600"
                                        >
                                            Ver Exemplo
                                        </button>
                                    </div>
                                </div>
                            )}

                            {/* Relatório Form */}
                            {activeTab === 'relatorio' && (
                                <div className="space-y-4">
                                    <h3 className="text-lg font-semibold text-slate-900">Relatório Psicológico</h3>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-slate-700 mb-2">Interessado</label>
                                            <input
                                                type="text"
                                                data-type="relatorio"
                                                data-field="interessado"
                                                value={formState.relatorio.interessado}
                                                onChange={handleInputChange}
                                                className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                placeholder="Nome do interessado"
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-slate-700 mb-2">Solicitante</label>
                                            <input
                                                type="text"
                                                data-type="relatorio"
                                                data-field="solicitante"
                                                value={formState.relatorio.solicitante}
                                                onChange={handleInputChange}
                                                className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                placeholder="Quem está solicitando"
                                            />
                                        </div>
                                        <div className="md:col-span-2">
                                            <label className="block text-sm font-medium text-slate-700 mb-2">Finalidade</label>
                                            <input
                                                type="text"
                                                data-type="relatorio"
                                                data-field="finalidade"
                                                value={formState.relatorio.finalidade}
                                                onChange={handleInputChange}
                                                className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                placeholder="Finalidade do relatório"
                                            />
                                        </div>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-slate-700 mb-2">Descrição da Demanda</label>
                                        <textarea
                                            data-type="relatorio"
                                            data-field="demanda"
                                            value={formState.relatorio.demanda}
                                            onChange={handleInputChange}
                                            rows={3}
                                            className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Descreva a demanda apresentada"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-slate-700 mb-2">Procedimento</label>
                                        <textarea
                                            data-type="relatorio"
                                            data-field="procedimento"
                                            value={formState.relatorio.procedimento}
                                            onChange={handleInputChange}
                                            rows={3}
                                            className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Descreva os procedimentos utilizados"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-slate-700 mb-2">Análise</label>
                                        <textarea
                                            data-type="relatorio"
                                            data-field="analise"
                                            value={formState.relatorio.analise}
                                            onChange={handleInputChange}
                                            rows={4}
                                            className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Análise dos dados coletados"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-slate-700 mb-2">Conclusão</label>
                                        <textarea
                                            data-type="relatorio"
                                            data-field="conclusao"
                                            value={formState.relatorio.conclusao}
                                            onChange={handleInputChange}
                                            rows={3}
                                            className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Conclusões do relatório"
                                        />
                                    </div>
                                    <div className="flex gap-3">
                                        <button
                                            onClick={() => previewTemplate('relatorio')}
                                            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                                        >
                                            Gerar Pré-visualização
                                        </button>
                                        <button
                                            onClick={() => showExample('relatorio')}
                                            className="px-4 py-2 bg-slate-500 text-white rounded-lg hover:bg-slate-600"
                                        >
                                            Ver Exemplo
                                        </button>
                                    </div>
                                </div>
                            )}

                            {/* Laudo Form */}
                            {activeTab === 'laudo' && (
                                <div className="space-y-4">
                                    <h3 className="text-lg font-semibold text-slate-900">Laudo Psicológico</h3>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-slate-700 mb-2">Interessado</label>
                                            <input
                                                type="text"
                                                data-type="laudo"
                                                data-field="interessado"
                                                value={formState.laudo.interessado}
                                                onChange={handleInputChange}
                                                className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                placeholder="Nome do interessado"
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-slate-700 mb-2">Solicitante</label>
                                            <input
                                                type="text"
                                                data-type="laudo"
                                                data-field="solicitante"
                                                value={formState.laudo.solicitante}
                                                onChange={handleInputChange}
                                                className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                placeholder="Quem está solicitando"
                                            />
                                        </div>
                                        <div className="md:col-span-2">
                                            <label className="block text-sm font-medium text-slate-700 mb-2">Finalidade</label>
                                            <input
                                                type="text"
                                                data-type="laudo"
                                                data-field="finalidade"
                                                value={formState.laudo.finalidade}
                                                onChange={handleInputChange}
                                                className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                placeholder="Finalidade do laudo"
                                            />
                                        </div>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-slate-700 mb-2">Descrição da Demanda</label>
                                        <textarea
                                            data-type="laudo"
                                            data-field="demanda"
                                            value={formState.laudo.demanda}
                                            onChange={handleInputChange}
                                            rows={3}
                                            className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Descreva a demanda apresentada"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-slate-700 mb-2">Procedimento</label>
                                        <textarea
                                            data-type="laudo"
                                            data-field="procedimento"
                                            value={formState.laudo.procedimento}
                                            onChange={handleInputChange}
                                            rows={3}
                                            className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Descreva os procedimentos utilizados"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-slate-700 mb-2">Análise</label>
                                        <textarea
                                            data-type="laudo"
                                            data-field="analise"
                                            value={formState.laudo.analise}
                                            onChange={handleInputChange}
                                            rows={4}
                                            className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Análise dos dados coletados"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-slate-700 mb-2">Conclusão</label>
                                        <textarea
                                            data-type="laudo"
                                            data-field="conclusao"
                                            value={formState.laudo.conclusao}
                                            onChange={handleInputChange}
                                            rows={3}
                                            className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Conclusões do laudo"
                                        />
                                    </div>
                                    <div className="flex gap-3">
                                        <button
                                            onClick={() => previewTemplate('laudo')}
                                            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                                        >
                                            Gerar Pré-visualização
                                        </button>
                                        <button
                                            onClick={() => showExample('laudo')}
                                            className="px-4 py-2 bg-slate-500 text-white rounded-lg hover:bg-slate-600"
                                        >
                                            Ver Exemplo
                                        </button>
                                    </div>
                                </div>
                            )}

                            {/* Parecer Form */}
                            {activeTab === 'parecer' && (
                                <div className="space-y-4">
                                    <h3 className="text-lg font-semibold text-slate-900">Parecer Psicológico</h3>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-slate-700 mb-2">Solicitante</label>
                                            <input
                                                type="text"
                                                data-type="parecer"
                                                data-field="solicitante"
                                                value={formState.parecer.solicitante}
                                                onChange={handleInputChange}
                                                className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                placeholder="Quem está solicitando"
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-slate-700 mb-2">Assunto</label>
                                            <input
                                                type="text"
                                                data-type="parecer"
                                                data-field="assunto"
                                                value={formState.parecer.assunto}
                                                onChange={handleInputChange}
                                                className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                placeholder="Assunto do parecer"
                                            />
                                        </div>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-slate-700 mb-2">Descrição da Demanda</label>
                                        <textarea
                                            data-type="parecer"
                                            data-field="demanda"
                                            value={formState.parecer.demanda}
                                            onChange={handleInputChange}
                                            rows={3}
                                            className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Descreva a demanda apresentada"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-slate-700 mb-2">Análise</label>
                                        <textarea
                                            data-type="parecer"
                                            data-field="analise"
                                            value={formState.parecer.analise}
                                            onChange={handleInputChange}
                                            rows={4}
                                            className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Análise técnica"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-slate-700 mb-2">Conclusão</label>
                                        <textarea
                                            data-type="parecer"
                                            data-field="conclusao"
                                            value={formState.parecer.conclusao}
                                            onChange={handleInputChange}
                                            rows={3}
                                            className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Conclusões do parecer"
                                        />
                                    </div>
                                    <div className="flex gap-3">
                                        <button
                                            onClick={() => previewTemplate('parecer')}
                                            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                                        >
                                            Gerar Pré-visualização
                                        </button>
                                        <button
                                            onClick={() => showExample('parecer')}
                                            className="px-4 py-2 bg-slate-500 text-white rounded-lg hover:bg-slate-600"
                                        >
                                            Ver Exemplo
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </main>

                {(modal.isOpen) && (
                    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
                        <div className="fixed inset-0 bg-slate-900/70 backdrop-blur-sm" onClick={() => setModal({ isOpen: false, type: '', content: '', title: '' })}></div>
                        <div className="bg-white rounded-lg shadow-2xl p-6 w-full max-w-3xl max-h-[90vh] flex flex-col" style={{animation: 'modalIn 0.3s ease-out forwards'}}>
                            <div className="flex justify-between items-center mb-4 pb-4 border-b">
                                <h3 className="text-xl font-bold text-slate-900">{modal.title}</h3>
                                <button onClick={() => setModal({ isOpen: false, type: '', content: '', title: '' })} className="text-slate-500 hover:text-slate-800 text-2xl">&times;</button>
                            </div>
                            <div className="overflow-y-auto flex-grow p-4 rounded-md bg-slate-50 border min-h-0">
                                <pre className="whitespace-pre-wrap word-wrap break-word font-serif text-slate-800">{modal.content}</pre>
                            </div>
                            <div className="mt-6 pt-4 border-t flex flex-wrap items-center justify-between gap-4">
                                {modal.type === 'preview' ? (
                                    <>
                                    <div className="flex flex-wrap gap-3">
                                        <button onClick={(e) => copyText(e.currentTarget, modal.content)} className="flex items-center gap-2 px-4 py-2 bg-slate-500 text-white font-semibold rounded-lg hover:bg-slate-600" disabled={signingStatus !== 'idle'}><CopyIcon /> <span>Copiar Texto</span></button>
                                        <button onClick={handleGovBrSignature} className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-wait" disabled={signingStatus !== 'idle'}>
                                            <GovBrIcon />
                                            <span>
                                                {signingStatus === 'idle' && 'Assinar com gov.br'}
                                                {signingStatus === 'generating' && 'Gerando PDF...'}
                                                {signingStatus === 'redirecting' && 'Redirecionando...'}
                                                {signingStatus === 'error' && 'Erro! Tente novamente'}
                                            </span>
                                        </button>
                                        <button onClick={exportPDF} className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white font-semibold rounded-lg hover:bg-red-700" disabled={signingStatus !== 'idle'}><DownloadIcon /> <span>Exportar PDF</span></button>
                                    </div>
                                    <button onClick={() => setModal({ isOpen: false, type: '', content: '', title: '' })} className="px-4 py-2 bg-transparent text-slate-600 font-semibold rounded-lg hover:bg-slate-200">Fechar</button>
                                    </>
                                ) : (
                                    <div className="w-full flex justify-end">
                                    <button onClick={() => setModal({ isOpen: false, type: '', content: '', title: '' })} className="px-4 py-2 bg-slate-200 text-slate-800 rounded-md hover:bg-slate-300">Fechar</button>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </>
    );
}

