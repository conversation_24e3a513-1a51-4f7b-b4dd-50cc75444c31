'use client';

import React, { useState, useEffect, useRef } from 'react';

// --- <PERSON><PERSON><PERSON> como Componentes React ---

const MenuIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-full h-full">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
    </svg>
);

const DocumentIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-full h-full">
        <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
    </svg>
);

const ClockIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-full h-full">
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
    </svg>
);

const CopyIcon = () => (
    <svg className="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75" />
    </svg>
);

const GovBrIcon = () => (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1.03 15.44l-3.5-3.5 1.41-1.41 2.09 2.09 4.59-4.59L18.41 9.5l-6.03 5.94z" />
    </svg>
);


const DownloadIcon = () => (
    <svg className="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3" />
    </svg>
);

// --- Componente principal da Aplicação ---
export default function GuiaDocumentosPsicologicos() {
    // --- State Hooks ---
    const [openAccordion, setOpenAccordion] = useState(null);
    const [activeTab, setActiveTab] = useState('declaracao');
    const [modal, setModal] = useState({ isOpen: false, type: '', content: '', title: '' });
    
    const [professionalInfo, setProfessionalInfo] = useState({ nome: '', crp: '' });
    const [logoImage, setLogoImage] = useState(null);
    const [currentDocumentForSigning, setCurrentDocumentForSigning] = useState(null);
    const [signingStatus, setSigningStatus] = useState('idle'); // idle, generating, redirecting, signing, success, error

    // Estado para os campos de cada tipo de documento
    const [formState, setFormState] = useState({
        declaracao: { paciente: '', finalidade: '', acompanhante: '', info: '' },
        atestado: { paciente: '', solicitante: '', finalidade: '', condicoes: '' },
        relatorio: { interessado: '', solicitante: '', finalidade: '', demanda: '', procedimento: '', analise: '', conclusao: '' },
        laudo: { interessado: '', solicitante: '', finalidade: '', demanda: '', procedimento: '', analise: '', conclusao: '' },
        parecer: { solicitante: '', assunto: '', demanda: '', analise: '', conclusao: '' },
    });
    
    // Refs para os inputs de arquivo
    const logoUploadRef = useRef(null);
    
    // --- Effect para carregar a biblioteca jsPDF do CDN ---
    useEffect(() => {
        const script = document.createElement('script');
        script.src = 'https://unpkg.com/jspdf@latest/dist/jspdf.umd.min.js';
        script.async = true;
        document.body.appendChild(script);

        return () => {
            document.body.removeChild(script);
        };
    }, []);

    // --- Handlers ---
    const handleAccordionToggle = (index) => {
        setOpenAccordion(openAccordion === index ? null : index);
    };
    
    const handleInputChange = (e) => {
        const { value, dataset } = e.target;
        const { type, field } = dataset;

        if (type === 'professional') {
            setProfessionalInfo(prev => ({ ...prev, [field]: value }));
        } else {
            setFormState(prev => ({
                ...prev,
                [type]: { ...prev[type], [field]: value }
            }));
        }
    };
    
    const handleFileChange = (e, setter) => {
        const file = e.target.files[0];
        if (file && file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = (event) => {
                setter(event.target.result);
            };
            reader.readAsDataURL(file);
        }
    };

    const getDocumentDataObject = (type) => {
        const date = new Date();
        const data = {
            professionalName: professionalInfo.nome || "Nome Completo da(o) Psicóloga(o)",
            professionalCrp: professionalInfo.crp || "CRP XX/XXXXX",
            city: "Ribeirão Preto",
            fullDate: `${date.getDate()} de ${date.toLocaleString('pt-BR', { month: 'long' })} de ${date.getFullYear()}`
        };
        const formData = formState[type];

        switch(type) {
            case 'declaracao':
                const d_acompanhanteText = formData.acompanhante ? ` e seu/sua acompanhante, ${formData.acompanhante},` : '';
                data.title = "DECLARAÇÃO";
                data.body = `Declaro, para fins de ${formData.finalidade || '[Finalidade]'}, que ${formData.paciente || '[Nome completo ou social]'}${d_acompanhanteText} esteve presente para a prestação de serviço psicológico.\n\n${formData.info || '[Informações sobre o serviço]'}`;
                break;
            case 'atestado':
                data.title = "ATESTADO PSICOLÓGICO";
                data.body = `Atesto, a pedido de ${formData.solicitante || '[Solicitante]'} e para fins de ${formData.finalidade || '[Finalidade]'}, que o(a) Sr(a). ${formData.paciente || '[Nome completo ou social]'} foi submetido(a) a avaliação psicológica e apresenta ${formData.condicoes || '[Descrição das condições psicológicas]'}.`;
                break;
            case 'relatorio':
                 data.title = "RELATÓRIO PSICOLÓGICO";
                 data.body = `1. IDENTIFICAÇÃO\nAutora/Relatora: ${data.professionalName} - ${data.professionalCrp}\nInteressado: ${formData.interessado || '[Preencha]'}\nSolicitante: ${formData.solicitante || '[Preencha]'}\nFinalidade: ${formData.finalidade || '[Preencha]'}\n\n2. DESCRIÇÃO DA DEMANDA\n${formData.demanda || '[Preencha]'}\n\n3. PROCEDIMENTO\n${formData.procedimento || '[Preencha]'}\n\n4. ANÁLISE\n${formData.analise || '[Preencha]'}\n\n5. CONCLUSÃO\n${formData.conclusao || '[Preencha]'}`;
                 break;
            case 'laudo':
                data.title = "LAUDO PSICOLÓGICO";
                data.body = `1. IDENTIFICAÇÃO\nAutora/Relatora: ${data.professionalName} - ${data.professionalCrp}\nInteressado: ${formData.interessado || '[Preencha]'}\nSolicitante: ${formData.solicitante || '[Preencha]'}\nFinalidade: ${formData.finalidade || '[Preencha]'}\n\n2. DESCRIÇÃO DA DEMANDA\n${formData.demanda || '[Preencha]'}\n\n3. PROCEDIMENTO\n${formData.procedimento || '[Preencha]'}\n\n4. ANÁLISE\n${formData.analise || '[Preencha]'}\n\n5. CONCLUSÃO\n${formData.conclusao || '[Preencha]'}\n\n6. REFERÊNCIAS\n[Listar em nota de rodapé as fontes científicas e referências bibliográficas utilizadas.]`;
                break;
            case 'parecer':
                 data.title = "PARECER PSICOLÓGICO";
                 data.body = `1. IDENTIFICAÇÃO\nParecerista: ${data.professionalName} - ${data.professionalCrp}\nSolicitante: ${formData.solicitante || '[Preencha]'}\nAssunto: ${formData.assunto || '[Preencha]'}\n\n2. DESCRIÇÃO DA DEMANDA\n${formData.demanda || '[Preencha]'}\n\n3. ANÁLISE\n${formData.analise || '[Preencha]'}\n\n4. CONCLUSÃO\n${formData.conclusao || '[Preencha]'}\n\n5. REFERÊNCIAS\n[Listar em nota de rodapé as fontes científicas e referências bibliográficas utilizadas.]`;
                 break;
            default:
                 data.title = "DOCUMENTO";
                 data.body = "Selecione um tipo de documento válido.";
        }
        return data;
    };

    const previewTemplate = (type) => {
        const data = getDocumentDataObject(type);
        const previewText = `${data.title}\n\n${data.body}\n\n${data.city}, ${data.fullDate}.\n\n___________________________________\n${data.professionalName}\n${data.professionalCrp}`;
        setCurrentDocumentForSigning({ type, data });
        setModal({ isOpen: true, type: 'preview', content: previewText, title: 'Pré-visualização do Documento' });
        setSigningStatus('idle');
    };

    const showExample = (type) => {
        const examples = { /* ... conteúdo dos exemplos aqui ... */ };
        setModal({ isOpen: true, type: 'example', content: examples[type]?.content || "Exemplo não disponível.", title: examples[type]?.title || "Exemplo" });
    };

    const copyText = (buttonElement, textToCopy) => {
        navigator.clipboard.writeText(textToCopy).then(() => { /* ... */ }).catch(err => console.error('Erro ao copiar o texto: ', err));
    };

    const generatePdfBlob = (docData) => {
        if (!window.jspdf) {
            console.error("jsPDF not loaded yet");
            alert("A biblioteca de PDF ainda não foi carregada. Tente novamente em alguns segundos.");
            return null;
        }
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF({ orientation: 'p', unit: 'mm', format: 'a4' });
        // ... (código de geração de PDF, sem alterações)
        const pageWidth = doc.internal.pageSize.getWidth();
        const margin = 20;
        const usableWidth = pageWidth - (margin * 2);
        let yPos = 20;
        if (logoImage) {
            const imgProps = doc.getImageProperties(logoImage);
            const logoHeight = 25;
            const logoWidth = (imgProps.width * logoHeight) / imgProps.height;
            const logoX = (pageWidth / 2) - (logoWidth / 2);
            doc.addImage(logoImage, 'PNG', logoX, yPos, logoWidth, logoHeight);
            yPos += logoHeight + 15;
        } else {
            yPos += 15;
        }
        doc.setFont('Times-Roman', 'bold');
        doc.setFontSize(16);
        doc.text(docData.title, pageWidth / 2, yPos, { align: 'center' });
        yPos += 20;
        doc.setFont('Times-Roman', 'normal');
        doc.setFontSize(12);
        const bodyLines = doc.splitTextToSize(docData.body, usableWidth);
        doc.text(bodyLines, margin, yPos);
        yPos += (bodyLines.length * 5) + 20;
        doc.text(`${docData.city}, ${docData.fullDate}.`, pageWidth - margin, yPos, { align: 'right' });
        yPos += 30;
        const lineXStart = (pageWidth / 2) - 40;
        doc.line(lineXStart, yPos, lineXStart + 80, yPos);
        yPos += 5;
        doc.text(docData.professionalName, pageWidth / 2, yPos, { align: 'center' });
        yPos += 5;
        doc.text(docData.professionalCrp, pageWidth / 2, yPos, { align: 'center' });
        return doc.output('blob');
    };
    
    const exportPDF = () => { /* ... (código sem alterações) ... */ };

    const handleGovBrSignature = async () => {
        if (!currentDocumentForSigning) {
            alert("Gere uma pré-visualização do documento primeiro.");
            return;
        }
        setSigningStatus('generating');

        const pdfBlob = generatePdfBlob(currentDocumentForSigning.data);
        if (!pdfBlob) {
            setSigningStatus('error');
            return;
        }

        const pdfFile = new File([pdfBlob], `${currentDocumentForSigning.data.title.toLowerCase().replace(/ /g, '_')}.pdf`, { type: 'application/pdf' });
        
        const formData = new FormData();
        formData.append('file', pdfFile);

        try {
            setSigningStatus('redirecting');
            const response = await fetch('/api/govbr/initiate-signature', {
                method: 'POST',
                body: formData,
            });

            if (!response.ok) throw new Error('Falha ao iniciar o processo no backend.');

            const { authorizationUrl } = await response.json();
            
            // Redireciona o usuário para o gov.br
            window.location.href = authorizationUrl;

        } catch (error) {
            console.error("Erro ao contatar o backend:", error);
            setSigningStatus('error');
            alert("Ocorreu um erro ao tentar iniciar a assinatura. Verifique o console.");
        }
    };
    
    // ... (resto do componente, renderização, etc., sem alterações)

    // O JSX que será renderizado
    return (
        <>
            <style>{`
                /* ... estilos CSS ... */
            `}</style>
            
            <div className="relative overflow-hidden bg-slate-50 text-slate-800">
                <main className="relative container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16 md:py-24 main-content">
                    {/* ... (Conteúdo principal do componente) ... */}
                </main>

                {(modal.isOpen) && (
                    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
                        <div className="fixed inset-0 bg-slate-900/70 backdrop-blur-sm" onClick={() => setModal({ isOpen: false })}></div>
                        <div className="bg-white rounded-lg shadow-2xl p-6 w-full max-w-3xl max-h-[90vh] flex flex-col" style={{animation: 'modalIn 0.3s ease-out forwards'}}>
                            <div className="flex justify-between items-center mb-4 pb-4 border-b">
                                <h3 className="text-xl font-bold text-slate-900">{modal.title}</h3>
                                <button onClick={() => setModal({ isOpen: false })} className="text-slate-500 hover:text-slate-800 text-2xl">&times;</button>
                            </div>
                            <div className="overflow-y-auto flex-grow p-4 rounded-md bg-slate-50 border min-h-0">
                                <pre className="whitespace-pre-wrap word-wrap break-word font-serif text-slate-800">{modal.content}</pre>
                            </div>
                            <div className="mt-6 pt-4 border-t flex flex-wrap items-center justify-between gap-4">
                                {modal.type === 'preview' ? (
                                    <>
                                    <div className="flex flex-wrap gap-3">
                                        <button onClick={(e) => copyText(e.currentTarget, modal.content)} className="flex items-center gap-2 px-4 py-2 bg-slate-500 text-white font-semibold rounded-lg hover:bg-slate-600" disabled={signingStatus !== 'idle'}><CopyIcon /> <span>Copiar Texto</span></button>
                                        <button onClick={handleGovBrSignature} className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-wait" disabled={signingStatus !== 'idle'}>
                                            <GovBrIcon />
                                            <span>
                                                {signingStatus === 'idle' && 'Assinar com gov.br'}
                                                {signingStatus === 'generating' && 'Gerando PDF...'}
                                                {signingStatus === 'redirecting' && 'Redirecionando...'}
                                                {signingStatus === 'error' && 'Erro! Tente novamente'}
                                            </span>
                                        </button>
                                        <button onClick={exportPDF} className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white font-semibold rounded-lg hover:bg-red-700" disabled={signingStatus !== 'idle'}><DownloadIcon /> <span>Exportar PDF</span></button>
                                    </div>
                                    <button onClick={() => setModal({ isOpen: false })} className="px-4 py-2 bg-transparent text-slate-600 font-semibold rounded-lg hover:bg-slate-200">Fechar</button>
                                    </>
                                ) : (
                                    <div className="w-full flex justify-end">
                                    <button onClick={() => setModal({ isOpen: false })} className="px-4 py-2 bg-slate-200 text-slate-800 rounded-md hover:bg-slate-300">Fechar</button>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </>
    );
}

