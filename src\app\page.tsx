'use client';

import React, { useState, useEffect } from 'react';

export default function GuiaDocumentosPsicologicos() {
    const [activeTab, setActiveTab] = useState('declaracao');
    const [openAccordion, setOpenAccordion] = useState<string | null>(null);

    // Load jsPDF
    useEffect(() => {
        const script = document.createElement('script');
        script.src = 'https://unpkg.com/jspdf@latest/dist/jspdf.umd.min.js';
        script.async = true;
        document.body.appendChild(script);
        return () => {
            if (document.body.contains(script)) {
                document.body.removeChild(script);
            }
        };
    }, []);

    const handleTabClick = (tabId: string) => {
        setActiveTab(tabId);
    };

    const toggleAccordion = (accordionId: string) => {
        setOpenAccordion(openAccordion === accordionId ? null : accordionId);
    };

    const previewTemplate = (type: string) => {
        alert(`Preview for ${type} - to be implemented`);
    };

    const showExample = (type: string) => {
        alert(`Example for ${type} - to be implemented`);
    };

    return (
        <div style={{ fontFamily: 'Inter, sans-serif', backgroundColor: '#f8fafc' }}>
            <div style={{ position: 'relative', overflow: 'hidden' }}>
                <div style={{
                    position: 'absolute',
                    inset: '0',
                    background: 'linear-gradient(to bottom right, #f8fafc, #eef2ff, #f8fafc)'
                }}></div>
                <main style={{
                    position: 'relative',
                    maxWidth: '80rem',
                    margin: '0 auto',
                    padding: '4rem 1rem 6rem'
                }}>
                    {/* Header */}
                    <header style={{ marginBottom: '5rem' }}>
                        <div style={{
                            display: 'grid',
                            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                            gap: '3rem',
                            alignItems: 'center'
                        }}>
                            <div style={{ textAlign: 'left' }}>
                                <h1 style={{
                                    fontSize: 'clamp(2rem, 5vw, 3.75rem)',
                                    fontWeight: '800',
                                    color: '#0f172a',
                                    letterSpacing: '-0.025em',
                                    marginBottom: '1.5rem'
                                }}>
                                    Guia da Resolução CFP nº 06/2019
                                </h1>
                                <p style={{
                                    fontSize: '1.125rem',
                                    color: '#475569',
                                    lineHeight: '1.7'
                                }}>
                                    Um guia interativo e prático para a elaboração de documentos escritos pela(o) psicóloga(o).
                                    Navegue pelas seções, entenda os princípios e gere modelos editáveis para a sua prática profissional.
                                </p>
                            </div>
                        </div>
                    </header>

                    {/* Accordion Container */}
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                        {/* First Accordion - Principles */}
                        <div style={{
                            backgroundColor: 'white',
                            borderRadius: '1rem',
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                            border: '1px solid #e2e8f0',
                            overflow: 'hidden'
                        }}>
                            <button
                                onClick={() => toggleAccordion('principles')}
                                style={{
                                    width: '100%',
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    padding: '1.5rem',
                                    textAlign: 'left',
                                    backgroundColor: 'transparent',
                                    border: 'none',
                                    cursor: 'pointer'
                                }}
                            >
                                <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                                    <div style={{
                                        width: '2.5rem',
                                        height: '2.5rem',
                                        padding: '0.5rem',
                                        borderRadius: '0.5rem',
                                        backgroundColor: '#e0e7ff',
                                        color: '#4338ca'
                                    }}>
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
                                        </svg>
                                    </div>
                                    <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#0f172a' }}>
                                        Seção I: Princípios Fundamentais
                                    </h2>
                                </div>
                                <div style={{
                                    fontSize: '1.875rem',
                                    color: '#94a3b8',
                                    fontWeight: '300',
                                    transform: openAccordion === 'principles' ? 'rotate(135deg)' : 'rotate(0deg)',
                                    transition: 'transform 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
                                }}>
                                    +
                                </div>
                            </button>
                            {openAccordion === 'principles' && (
                                <div style={{
                                    borderTop: '1px solid #e2e8f0',
                                    padding: '1.5rem',
                                    color: '#475569'
                                }}>
                                    <p style={{ marginBottom: '1.5rem' }}>
                                        Esta seção estabelece a base para toda a comunicação escrita. Um documento psicológico não é apenas um papel,
                                        mas um instrumento que reflete o raciocínio técnico, a ética profissional e o compromisso científico da(o) psicóloga(o).
                                        É a materialização do serviço prestado.
                                    </p>
                                    <div style={{
                                        display: 'grid',
                                        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                                        gap: '1rem'
                                    }}>
                                        <div style={{
                                            padding: '1rem',
                                            backgroundColor: '#f8fafc',
                                            borderRadius: '0.5rem',
                                            border: '1px solid #e2e8f0'
                                        }}>
                                            <h4 style={{ fontWeight: '600', color: '#1e293b' }}>Princípios Técnicos</h4>
                                            <p style={{ fontSize: '0.875rem', marginTop: '0.25rem' }}>
                                                O documento deve ser fidedigno, baseado em dados que validam o pensamento psicológico.
                                                Deve considerar o contexto histórico-social do indivíduo, a natureza dinâmica do fenômeno
                                                psicológico e ser fundamentado em ciência, ética e legislação.
                                            </p>
                                        </div>
                                        <div style={{
                                            padding: '1rem',
                                            backgroundColor: '#f8fafc',
                                            borderRadius: '0.5rem',
                                            border: '1px solid #e2e8f0'
                                        }}>
                                            <h4 style={{ fontWeight: '600', color: '#1e293b' }}>Princípios da Linguagem</h4>
                                            <p style={{ fontSize: '0.875rem', marginTop: '0.25rem' }}>
                                                Exige-se linguagem impessoal (3ª pessoa), precisa e com nexo causal. O texto deve seguir a norma culta
                                                da língua portuguesa, ser objetivo e garantir os direitos humanos. Descrições literais de atendimentos
                                                devem ser evitadas, salvo justificativa técnica clara.
                                            </p>
                                        </div>
                                        <div style={{
                                            padding: '1rem',
                                            backgroundColor: '#f8fafc',
                                            borderRadius: '0.5rem',
                                            border: '1px solid #e2e8f0'
                                        }}>
                                            <h4 style={{ fontWeight: '600', color: '#1e293b' }}>Princípios Éticos</h4>
                                            <p style={{ fontSize: '0.875rem', marginTop: '0.25rem' }}>
                                                O sigilo profissional é central e inegociável. O documento deve ser construído para garantir os direitos
                                                humanos, evitando sustentar modelos institucionais de segregação. É dever da(o) psicóloga(o) fornecer o
                                                documento sempre que solicitado ou ao final de uma avaliação.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Second Accordion - Document Forms */}
                        <div style={{
                            backgroundColor: 'white',
                            borderRadius: '1rem',
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                            border: '1px solid #e2e8f0',
                            overflow: 'hidden'
                        }}>
                            <button
                                onClick={() => toggleAccordion('documents')}
                                style={{
                                    width: '100%',
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    padding: '1.5rem',
                                    textAlign: 'left',
                                    backgroundColor: 'transparent',
                                    border: 'none',
                                    cursor: 'pointer'
                                }}
                            >
                                <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                                    <div style={{
                                        width: '2.5rem',
                                        height: '2.5rem',
                                        padding: '0.5rem',
                                        borderRadius: '0.5rem',
                                        backgroundColor: '#dbeafe',
                                        color: '#2563eb'
                                    }}>
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
                                        </svg>
                                    </div>
                                    <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#0f172a' }}>
                                        Seções II & III: Modalidades e Estruturas
                                    </h2>
                                </div>
                                <div style={{
                                    fontSize: '1.875rem',
                                    color: '#94a3b8',
                                    fontWeight: '300',
                                    transform: openAccordion === 'documents' ? 'rotate(135deg)' : 'rotate(0deg)',
                                    transition: 'transform 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
                                }}>
                                    +
                                </div>
                            </button>
                            {openAccordion === 'documents' && (
                                <div style={{
                                    borderTop: '1px solid #e2e8f0',
                                    padding: '1.5rem',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    gap: '2rem'
                                }}>
                                    {/* Professional Info Section */}
                                    <div style={{
                                        padding: '1rem',
                                        backgroundColor: '#eef2ff',
                                        borderLeft: '4px solid #6366f1',
                                        borderRadius: '0 0.5rem 0.5rem 0',
                                        display: 'flex',
                                        flexDirection: 'column',
                                        gap: '1rem'
                                    }}>
                                        <div>
                                            <h4 style={{ fontWeight: 'bold', color: '#1e293b', marginBottom: '0.5rem' }}>
                                                Informações do Profissional (para todos os modelos)
                                            </h4>
                                            <div style={{
                                                display: 'grid',
                                                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                                                gap: '1rem'
                                            }}>
                                                <div>
                                                    <label style={{
                                                        display: 'block',
                                                        fontWeight: '500',
                                                        color: '#475569',
                                                        marginBottom: '0.25rem',
                                                        fontSize: '0.875rem'
                                                    }}>
                                                        Nome Completo
                                                    </label>
                                                    <input
                                                        type="text"
                                                        placeholder="Seu nome completo ou social"
                                                        style={{
                                                            width: '100%',
                                                            padding: '0.5rem 0.75rem',
                                                            border: '1px solid #cbd5e1',
                                                            borderRadius: '0.375rem',
                                                            fontSize: '0.875rem'
                                                        }}
                                                    />
                                                </div>
                                                <div>
                                                    <label style={{
                                                        display: 'block',
                                                        fontWeight: '500',
                                                        color: '#475569',
                                                        marginBottom: '0.25rem',
                                                        fontSize: '0.875rem'
                                                    }}>
                                                        CRP
                                                    </label>
                                                    <input
                                                        type="text"
                                                        placeholder="XX/XXXXX"
                                                        style={{
                                                            width: '100%',
                                                            padding: '0.5rem 0.75rem',
                                                            border: '1px solid #cbd5e1',
                                                            borderRadius: '0.375rem',
                                                            fontSize: '0.875rem'
                                                        }}
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                        <div style={{ paddingTop: '1rem', borderTop: '1px solid #c7d2fe' }}>
                                            <h4 style={{ fontWeight: 'bold', color: '#1e293b', marginBottom: '0.5rem' }}>
                                                Identidade Visual (Opcional)
                                            </h4>
                                            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                                                <div style={{
                                                    width: '5rem',
                                                    height: '5rem',
                                                    borderRadius: '0.375rem',
                                                    backgroundColor: 'white',
                                                    border: '2px dashed #a5b4fc',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    fontSize: '0.75rem',
                                                    color: '#6366f1'
                                                }}>
                                                    Logo
                                                </div>
                                                <div>
                                                    <label style={{
                                                        display: 'block',
                                                        fontWeight: '500',
                                                        color: '#475569',
                                                        marginBottom: '0.25rem',
                                                        fontSize: '0.875rem'
                                                    }}>
                                                        Logo da Clínica/Consultório
                                                    </label>
                                                    <button style={{
                                                        padding: '0.5rem 1rem',
                                                        fontSize: '0.875rem',
                                                        backgroundColor: 'white',
                                                        color: '#4338ca',
                                                        fontWeight: '600',
                                                        borderRadius: '0.5rem',
                                                        border: '1px solid #a5b4fc',
                                                        cursor: 'pointer'
                                                    }}>
                                                        Carregar Imagem
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Tabs Container */}
                                    <div style={{ borderBottom: '1px solid #e2e8f0' }}>
                                        <nav style={{ display: 'flex', flexWrap: 'wrap', marginBottom: '-1px' }}>
                                            {[
                                                { id: 'declaracao', name: 'Declaração' },
                                                { id: 'atestado', name: 'Atestado' },
                                                { id: 'relatorio', name: 'Relatório' },
                                                { id: 'laudo', name: 'Laudo' },
                                                { id: 'parecer', name: 'Parecer' }
                                            ].map((tab) => (
                                                <button
                                                    key={tab.id}
                                                    onClick={() => handleTabClick(tab.id)}
                                                    style={{
                                                        padding: '0.5rem 1rem',
                                                        borderBottom: activeTab === tab.id ? '2px solid #4f46e5' : '2px solid transparent',
                                                        fontWeight: activeTab === tab.id ? '600' : '500',
                                                        color: activeTab === tab.id ? '#4f46e5' : '#64748b',
                                                        backgroundColor: 'transparent',
                                                        border: 'none',
                                                        cursor: 'pointer',
                                                        transition: 'color 0.3s, border-color 0.3s'
                                                    }}
                                                >
                                                    {tab.name}
                                                </button>
                                            ))}
                                        </nav>
                                    </div>

                                    {/* Tab Panels */}
                                    <div>
                                        {/* Declaração Panel */}
                                        {activeTab === 'declaracao' && (
                                            <div style={{ paddingBottom: '1rem' }}>
                                                <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#1e293b' }}>Declaração</h3>
                                                <p style={{ color: '#475569', marginTop: '0.25rem' }}>
                                                    Um documento que registra informações objetivas sobre o serviço prestado (ex: comparecimento, horários),
                                                    sem incluir dados sobre o estado psicológico do atendido.
                                                </p>
                                                <div style={{
                                                    marginTop: '1rem',
                                                    padding: '1rem',
                                                    backgroundColor: '#f1f5f9',
                                                    border: '1px solid #e2e8f0',
                                                    borderRadius: '0.5rem'
                                                }}>
                                                    <h5 style={{ fontWeight: '600', color: '#1e293b' }}>Gerador de Modelo - Declaração</h5>
                                                    <div style={{
                                                        display: 'grid',
                                                        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                                                        gap: '1rem',
                                                        marginTop: '1rem'
                                                    }}>
                                                        <div>
                                                            <label style={{
                                                                display: 'block',
                                                                fontWeight: '500',
                                                                color: '#475569',
                                                                marginBottom: '0.25rem',
                                                                fontSize: '0.875rem'
                                                            }}>
                                                                Nome do Atendido
                                                            </label>
                                                            <input
                                                                type="text"
                                                                placeholder="Nome completo ou social"
                                                                style={{
                                                                    width: '100%',
                                                                    padding: '0.5rem 0.75rem',
                                                                    border: '1px solid #cbd5e1',
                                                                    borderRadius: '0.375rem',
                                                                    fontSize: '0.875rem'
                                                                }}
                                                            />
                                                        </div>
                                                        <div>
                                                            <label style={{
                                                                display: 'block',
                                                                fontWeight: '500',
                                                                color: '#475569',
                                                                marginBottom: '0.25rem',
                                                                fontSize: '0.875rem'
                                                            }}>
                                                                Finalidade
                                                            </label>
                                                            <input
                                                                type="text"
                                                                placeholder="Ex: Para fins de comprovação de presença"
                                                                style={{
                                                                    width: '100%',
                                                                    padding: '0.5rem 0.75rem',
                                                                    border: '1px solid #cbd5e1',
                                                                    borderRadius: '0.375rem',
                                                                    fontSize: '0.875rem'
                                                                }}
                                                            />
                                                        </div>
                                                        <div>
                                                            <label style={{
                                                                display: 'block',
                                                                fontWeight: '500',
                                                                color: '#475569',
                                                                marginBottom: '0.25rem',
                                                                fontSize: '0.875rem'
                                                            }}>
                                                                Acompanhante (Opcional)
                                                            </label>
                                                            <input
                                                                type="text"
                                                                placeholder="Nome do acompanhante"
                                                                style={{
                                                                    width: '100%',
                                                                    padding: '0.5rem 0.75rem',
                                                                    border: '1px solid #cbd5e1',
                                                                    borderRadius: '0.375rem',
                                                                    fontSize: '0.875rem'
                                                                }}
                                                            />
                                                        </div>
                                                        <div style={{ gridColumn: '1 / -1' }}>
                                                            <label style={{
                                                                display: 'block',
                                                                fontWeight: '500',
                                                                color: '#475569',
                                                                marginBottom: '0.25rem',
                                                                fontSize: '0.875rem'
                                                            }}>
                                                                Informações sobre o Serviço
                                                            </label>
                                                            <textarea
                                                                placeholder="Ex: Compareceu ao acompanhamento psicológico no dia XX/XX/XXXX, das XX:XX às XX:XX."
                                                                style={{
                                                                    width: '100%',
                                                                    padding: '0.5rem 0.75rem',
                                                                    border: '1px solid #cbd5e1',
                                                                    borderRadius: '0.375rem',
                                                                    fontSize: '0.875rem',
                                                                    minHeight: '100px'
                                                                }}
                                                            />
                                                        </div>
                                                    </div>
                                                    <div style={{ display: 'flex', gap: '1rem', marginTop: '1rem' }}>
                                                        <button
                                                            onClick={() => previewTemplate('declaracao')}
                                                            style={{
                                                                backgroundColor: '#3b82f6',
                                                                color: 'white',
                                                                padding: '0.5rem 1rem',
                                                                borderRadius: '0.375rem',
                                                                border: 'none',
                                                                cursor: 'pointer'
                                                            }}
                                                        >
                                                            Pré-visualizar Modelo
                                                        </button>
                                                        <button
                                                            onClick={() => showExample('declaracao')}
                                                            style={{
                                                                backgroundColor: '#cbd5e1',
                                                                color: '#1e293b',
                                                                padding: '0.5rem 1rem',
                                                                borderRadius: '0.375rem',
                                                                border: 'none',
                                                                cursor: 'pointer'
                                                            }}
                                                        >
                                                            Ver Exemplo
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        {/* Atestado Panel */}
                                        {activeTab === 'atestado' && (
                                            <div style={{ paddingBottom: '1rem' }}>
                                                <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#1e293b' }}>Atestado Psicológico</h3>
                                                <p style={{ color: '#475569', marginTop: '0.25rem' }}>
                                                    Resulta de uma avaliação psicológica e certifica uma determinada situação ou estado psicológico,
                                                    podendo justificar faltas, aptidão para atividades ou solicitar afastamento.
                                                </p>
                                                <div style={{
                                                    marginTop: '1rem',
                                                    padding: '1rem',
                                                    backgroundColor: '#f1f5f9',
                                                    border: '1px solid #e2e8f0',
                                                    borderRadius: '0.5rem'
                                                }}>
                                                    <h5 style={{ fontWeight: '600', color: '#1e293b' }}>Gerador de Modelo - Atestado</h5>
                                                    <div style={{
                                                        display: 'grid',
                                                        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                                                        gap: '1rem',
                                                        marginTop: '1rem'
                                                    }}>
                                                        <div>
                                                            <label style={{
                                                                display: 'block',
                                                                fontWeight: '500',
                                                                color: '#475569',
                                                                marginBottom: '0.25rem',
                                                                fontSize: '0.875rem'
                                                            }}>
                                                                Nome do Atendido
                                                            </label>
                                                            <input
                                                                type="text"
                                                                placeholder="Nome completo ou social"
                                                                style={{
                                                                    width: '100%',
                                                                    padding: '0.5rem 0.75rem',
                                                                    border: '1px solid #cbd5e1',
                                                                    borderRadius: '0.375rem',
                                                                    fontSize: '0.875rem'
                                                                }}
                                                            />
                                                        </div>
                                                        <div>
                                                            <label style={{
                                                                display: 'block',
                                                                fontWeight: '500',
                                                                color: '#475569',
                                                                marginBottom: '0.25rem',
                                                                fontSize: '0.875rem'
                                                            }}>
                                                                Nome do Solicitante
                                                            </label>
                                                            <input
                                                                type="text"
                                                                placeholder="Ex: A própria pessoa, Empresa X"
                                                                style={{
                                                                    width: '100%',
                                                                    padding: '0.5rem 0.75rem',
                                                                    border: '1px solid #cbd5e1',
                                                                    borderRadius: '0.375rem',
                                                                    fontSize: '0.875rem'
                                                                }}
                                                            />
                                                        </div>
                                                        <div style={{ gridColumn: '1 / -1' }}>
                                                            <label style={{
                                                                display: 'block',
                                                                fontWeight: '500',
                                                                color: '#475569',
                                                                marginBottom: '0.25rem',
                                                                fontSize: '0.875rem'
                                                            }}>
                                                                Finalidade
                                                            </label>
                                                            <input
                                                                type="text"
                                                                placeholder="Ex: Aptidão para o cargo de..., Justificativa de falta"
                                                                style={{
                                                                    width: '100%',
                                                                    padding: '0.5rem 0.75rem',
                                                                    border: '1px solid #cbd5e1',
                                                                    borderRadius: '0.375rem',
                                                                    fontSize: '0.875rem'
                                                                }}
                                                            />
                                                        </div>
                                                        <div style={{ gridColumn: '1 / -1' }}>
                                                            <label style={{
                                                                display: 'block',
                                                                fontWeight: '500',
                                                                color: '#475569',
                                                                marginBottom: '0.25rem',
                                                                fontSize: '0.875rem'
                                                            }}>
                                                                Descrição das Condições Psicológicas
                                                            </label>
                                                            <textarea
                                                                placeholder="Atesto que o(a) Sr(a)... apresenta (descrever condições, CID se autorizado)..."
                                                                style={{
                                                                    width: '100%',
                                                                    padding: '0.5rem 0.75rem',
                                                                    border: '1px solid #cbd5e1',
                                                                    borderRadius: '0.375rem',
                                                                    fontSize: '0.875rem',
                                                                    minHeight: '100px'
                                                                }}
                                                            />
                                                        </div>
                                                    </div>
                                                    <div style={{ display: 'flex', gap: '1rem', marginTop: '1rem' }}>
                                                        <button
                                                            onClick={() => previewTemplate('atestado')}
                                                            style={{
                                                                backgroundColor: '#3b82f6',
                                                                color: 'white',
                                                                padding: '0.5rem 1rem',
                                                                borderRadius: '0.375rem',
                                                                border: 'none',
                                                                cursor: 'pointer'
                                                            }}
                                                        >
                                                            Pré-visualizar Modelo
                                                        </button>
                                                        <button
                                                            onClick={() => showExample('atestado')}
                                                            style={{
                                                                backgroundColor: '#cbd5e1',
                                                                color: '#1e293b',
                                                                padding: '0.5rem 1rem',
                                                                borderRadius: '0.375rem',
                                                                border: 'none',
                                                                cursor: 'pointer'
                                                            }}
                                                        >
                                                            Ver Exemplo
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        {/* Other tabs - simplified for now */}
                                        {activeTab === 'relatorio' && (
                                            <div style={{ paddingBottom: '1rem' }}>
                                                <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#1e293b' }}>Relatório Psicológico</h3>
                                                <p style={{ color: '#475569', marginTop: '0.25rem' }}>
                                                    Comunica a atuação profissional em um processo de trabalho, podendo gerar orientações e encaminhamentos,
                                                    mas **não** tem como finalidade produzir diagnóstico psicológico.
                                                </p>
                                            </div>
                                        )}

                                        {activeTab === 'laudo' && (
                                            <div style={{ paddingBottom: '1rem' }}>
                                                <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#1e293b' }}>Laudo Psicológico</h3>
                                                <p style={{ color: '#475569', marginTop: '0.25rem' }}>
                                                    É o resultado de um processo de avaliação psicológica, com finalidade de subsidiar decisões.
                                                    Apresenta informações técnicas e científicas, e a citação de referências é obrigatória.
                                                </p>
                                            </div>
                                        )}

                                        {activeTab === 'parecer' && (
                                            <div style={{ paddingBottom: '1rem' }}>
                                                <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#1e293b' }}>Parecer Psicológico</h3>
                                                <p style={{ color: '#475569', marginTop: '0.25rem' }}>
                                                    É um pronunciamento por escrito que responde a uma consulta sobre uma questão-problema ou a um documento
                                                    psicológico questionado. Não é resultado de uma avaliação ou intervenção direta.
                                                </p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </main>
            </div>
        </div>
    );
}
