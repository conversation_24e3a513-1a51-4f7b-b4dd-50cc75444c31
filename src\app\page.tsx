'use client';

import React, { useState, useEffect } from 'react';

export default function GuiaDocumentosPsicologicos() {
    const [activeTab, setActiveTab] = useState('declaracao');
    const [openAccordion, setOpenAccordion] = useState<string | null>(null);
    const [modal, setModal] = useState<{isOpen: boolean, type: 'preview' | 'example', content: string, title: string}>({
        isOpen: false,
        type: 'preview',
        content: '',
        title: ''
    });
    const [formData, setFormData] = useState({
        professional: { nome: '', crp: '' },
        declaracao: { paciente: '', finalidade: '', acompanhante: '', info: '' },
        atestado: { paciente: '', solicitante: '', finalidade: '', condicoes: '' },
        relatorio: { interessado: '', solicitante: '', finalidade: '', demanda: '', procedimento: '', analise: '', conclusao: '' },
        laudo: { interessado: '', solicitante: '', finalidade: '', demanda: '', procedimento: '', analise: '', conclusao: '' },
        parecer: { solicitante: '', assunto: '', demanda: '', analise: '', conclusao: '' }
    });

    // Load jsPDF
    useEffect(() => {
        const script = document.createElement('script');
        script.src = 'https://unpkg.com/jspdf@latest/dist/jspdf.umd.min.js';
        script.async = true;
        document.body.appendChild(script);
        return () => {
            if (document.body.contains(script)) {
                document.body.removeChild(script);
            }
        };
    }, []);

    const handleTabClick = (tabId: string) => {
        setActiveTab(tabId);
    };

    const toggleAccordion = (accordionId: string) => {
        setOpenAccordion(openAccordion === accordionId ? null : accordionId);
    };

    const handleInputChange = (section: string, field: string, value: string) => {
        setFormData(prev => ({
            ...prev,
            [section]: {
                ...prev[section as keyof typeof prev],
                [field]: value
            }
        }));
    };

    const getDocumentData = (type: string) => {
        const date = new Date();
        const data = {
            professionalName: formData.professional.nome || "Nome Completo da(o) Psicóloga(o)",
            professionalCrp: formData.professional.crp || "CRP XX/XXXXX",
            city: "Ribeirão Preto",
            fullDate: `${date.getDate()} de ${date.toLocaleString('pt-BR', { month: 'long' })} de ${date.getFullYear()}`
        };

        let title = '';
        let body = '';

        switch(type) {
            case 'declaracao':
                const d = formData.declaracao;
                const acompanhanteText = d.acompanhante ? ` e seu/sua acompanhante, ${d.acompanhante},` : '';
                title = "DECLARAÇÃO";
                body = `Declaro, para fins de ${d.finalidade || '[Finalidade]'}, que ${d.paciente || '[Nome completo ou social]'}${acompanhanteText} esteve presente para a prestação de serviço psicológico.\n\n${d.info || '[Informações sobre o serviço]'}`;
                break;
            case 'atestado':
                const a = formData.atestado;
                title = "ATESTADO PSICOLÓGICO";
                body = `Atesto, a pedido de ${a.solicitante || '[Solicitante]'} e para fins de ${a.finalidade || '[Finalidade]'}, que o(a) Sr(a). ${a.paciente || '[Nome completo ou social]'} foi submetido(a) a avaliação psicológica e apresenta ${a.condicoes || '[Descrição das condições psicológicas]'}.`;
                break;
            case 'relatorio':
                const r = formData.relatorio;
                title = "RELATÓRIO PSICOLÓGICO";
                body = `1. IDENTIFICAÇÃO\nAutora/Relatora: ${data.professionalName} - ${data.professionalCrp}\nInteressado: ${r.interessado || '[Preencha]'}\nSolicitante: ${r.solicitante || '[Preencha]'}\nFinalidade: ${r.finalidade || '[Preencha]'}\n\n2. DESCRIÇÃO DA DEMANDA\n${r.demanda || '[Preencha]'}\n\n3. PROCEDIMENTO\n${r.procedimento || '[Preencha]'}\n\n4. ANÁLISE\n${r.analise || '[Preencha]'}\n\n5. CONCLUSÃO\n${r.conclusao || '[Preencha]'}`;
                break;
            case 'laudo':
                const l = formData.laudo;
                title = "LAUDO PSICOLÓGICO";
                body = `1. IDENTIFICAÇÃO\nAutora/Relatora: ${data.professionalName} - ${data.professionalCrp}\nInteressado: ${l.interessado || '[Preencha]'}\nSolicitante: ${l.solicitante || '[Preencha]'}\nFinalidade: ${l.finalidade || '[Preencha]'}\n\n2. DESCRIÇÃO DA DEMANDA\n${l.demanda || '[Preencha]'}\n\n3. PROCEDIMENTO\n${l.procedimento || '[Preencha]'}\n\n4. ANÁLISE\n${l.analise || '[Preencha]'}\n\n5. CONCLUSÃO\n${l.conclusao || '[Preencha]'}\n\n6. REFERÊNCIAS\n[Listar em nota de rodapé as fontes científicas e referências bibliográficas utilizadas.]`;
                break;
            case 'parecer':
                const p = formData.parecer;
                title = "PARECER PSICOLÓGICO";
                body = `1. IDENTIFICAÇÃO\nParecerista: ${data.professionalName} - ${data.professionalCrp}\nSolicitante: ${p.solicitante || '[Preencha]'}\nAssunto: ${p.assunto || '[Preencha]'}\n\n2. DESCRIÇÃO DA DEMANDA\n${p.demanda || '[Preencha]'}\n\n3. ANÁLISE\n${p.analise || '[Preencha]'}\n\n4. CONCLUSÃO\n${p.conclusao || '[Preencha]'}\n\n5. REFERÊNCIAS\n[Listar em nota de rodapé as fontes científicas e referências bibliográficas utilizadas.]`;
                break;
        }

        return { ...data, title, body };
    };

    const previewTemplate = (type: string) => {
        const data = getDocumentData(type);
        const previewText = `${data.title}\n\n${data.body}\n\n${data.city}, ${data.fullDate}.\n\n___________________________________\n${data.professionalName}\n${data.professionalCrp}`;
        setModal({
            isOpen: true,
            type: 'preview',
            content: previewText,
            title: 'Pré-visualização do Documento'
        });
    };

    const showExample = (type: string) => {
        const examples: Record<string, {title: string, content: string}> = {
            declaracao: {
                title: "Exemplo de Declaração",
                content: `DECLARAÇÃO\n\nDeclaro, para fins de apresentação junto à empresa X, que Maria da Silva (nome social: Maria da Silva) esteve presente em sessão de psicoterapia no dia 11 de setembro de 2025, das 14:00 às 14:50.\n\nSão Paulo, 11 de setembro de 2025.\n\n___________________________________\nDr. João Carlos Pereira\nCRP 06/12345`
            },
            atestado: {
                title: "Exemplo de Atestado Psicológico",
                content: `ATESTADO PSICOLÓGICO\n\nAtesto, para fins de apresentação em perícia do INSS, a pedido de Joana Martins, que a referida paciente, após processo de avaliação psicológica, apresenta quadro compatível com Transtorno de Ansiedade Generalizada (CID-10 F41.1), com sintomas significativos de angústia, preocupação excessiva e tensão muscular, que atualmente a incapacitam para o exercício de suas atividades laborais. Sugere-se afastamento do trabalho por um período de 30 (trinta) dias para tratamento.\n\nCuritiba, 11 de setembro de 2025.\n\n___________________________________\nDra. Ana Beatriz Costa\nCRP 08/67890`
            },
            relatorio: {
                title: "Exemplo de Relatório Psicológico",
                content: `RELATÓRIO PSICOLÓGICO\n\n1. IDENTIFICAÇÃO\nAutora/Relatora: Dra. Sofia Lima Santos - CRP 05/54321\nInteressado: Carlos Eduardo Oliveira\nSolicitante: Escola Primária Aprender Feliz\nFinalidade: Apresentar informações sobre o acompanhamento psicológico para subsidiar o plano de desenvolvimento individual do aluno.\n\n2. DESCRIÇÃO DA DEMANDA\nO presente relatório foi solicitado pela coordenação pedagógica da escola, com o consentimento dos responsáveis, para compreender as dificuldades de interação social e episódios de agitação motora de Carlos em sala de aula, conforme relatado pela professora.\n\n3. PROCEDIMENTO\nForam realizadas 8 sessões de ludoterapia, com frequência semanal, entre julho e setembro de 2025. Utilizou-se a abordagem da Terapia Cognitivo-Comportamental, com técnicas de psicoeducação emocional, treino de habilidades sociais e observação lúdica. Foram realizadas também 2 sessões de orientação com os pais.\n\n4. ANÁLISE\nDurante as sessões, Carlos demonstrou dificuldade em identificar e nomear emoções como frustração e raiva, reagindo com agitação quando contrariado. Observou-se, no brincar, uma preferência por atividades solitárias e dificuldade em seguir regras em jogos compartilhados. Contudo, mostrou-se receptivo às intervenções de psicoeducação, começando a identificar emoções básicas em si e nos outros. A agitação parece ser um comportamento reativo a situações de sobrecarga social ou frustração não verbalizada.\n\n5. CONCLUSÃO\nConclui-se que as dificuldades de Carlos estão relacionadas a um repertório de habilidades sociais e de regulação emocional ainda em desenvolvimento. Recomenda-se a continuidade do acompanhamento psicológico para fortalecer tais habilidades e sugere-se à escola a implementação de estratégias de mediação em sala, como a criação de um "espaço da calma" e o reforço positivo de comportamentos pró-sociais.\n\nRio de Janeiro, 11 de setembro de 2025.\n\n___________________________________\nDra. Sofia Lima Santos\nCRP 05/54321`
            },
            laudo: {
                title: "Exemplo de Laudo Psicológico",
                content: `LAUDO PSICOLÓGICO\n\n1. IDENTIFICAÇÃO\nAutora/Relatora: Dra. Beatriz Almeida - CRP 07/11223\nInteressado: Roberto Menezes\nSolicitante: Dr. Fernando Guimarães, Neurologista\nFinalidade: Avaliação neuropsicológica para investigação de queixas de memória e auxílio em diagnóstico diferencial.\n\n2. DESCRIÇÃO DA DEMANDA\nPaciente de 68 anos, encaminhado pelo neurologista assistente, com queixas de esquecimentos progressivos há cerca de um ano, principalmente para fatos recentes, e dificuldade em encontrar palavras.\n\n3. PROCEDIMENTO\nO processo de avaliação ocorreu em 4 sessões, incluindo entrevista de anamnese com o paciente e sua esposa, e aplicação dos seguintes instrumentos: Mini-Exame do Estado Mental (MEEM), Teste de Aprendizagem Auditivo-Verbal de Rey (RAVLT), Figuras Complexas de Rey, Teste do Relógio, Fluência Verbal (semântica e fonêmica) e Escala de Depressão Geriátrica (GDS-15).\n\n4. ANÁLISE\nOs resultados indicam desempenho cognitivo global abaixo do esperado para a idade e escolaridade (MEEM=22/30). A avaliação da memória evidenciou prejuízo significativo na capacidade de aprendizagem e evocação de novas informações (curva de aprendizagem do RAVLT foi deficitária, com baixa recuperação após intervalo). A memória visual também se mostrou comprometida. Funções executivas e linguagem apresentaram déficits leves, notadamente na evocação lexical (baixa pontuação na fluência verbal). Não foram observados sintomas depressivos clinicamente significativos (GDS=3).\n\n5. CONCLUSÃO\nOs resultados da avaliação neuropsicológica são compatíveis com um perfil de comprometimento cognitivo de múltiplas funções, com predomínio de déficit de memória episódica. A hipótese diagnóstica é de um Transtorno Neurocognitivo Maior, possivelmente devido à Doença de Alzheimer (CID-10 G30). Sugere-se a continuidade da investigação médica e o início de um programa de reabilitação cognitiva para gerenciamento dos déficits no cotidiano.\n\n6. REFERÊNCIAS\n(As referências dos testes seriam listadas aqui, em notas de rodapé no documento final).\n\nPorto Alegre, 11 de setembro de 2025.\n\n___________________________________\nDra. Beatriz Almeida\nCRP 07/11223`
            },
            parecer: {
                title: "Exemplo de Parecer Psicológico",
                content: `PARECER PSICOLÓGICO\n\n1. IDENTIFICAÇÃO\nParecerista: Dr. Marcos Vinicius Andrade - CRP 01/99887\nSolicitante: Dr. Advogado José da Costa\nAssunto: Análise técnica do Laudo Psicológico datado de 10/08/2025, presente nos autos do processo nº 12345-67.2025.\n\n2. DESCRIÇÃO DA DEMANDA\nFoi solicitado um parecer técnico sobre a consistência metodológica, técnica e ética do laudo psicológico elaborado pela psicóloga perita no processo de disputa de guarda, que avaliou as condições psicológicas dos genitores.\n\n3. ANÁLISE\nO laudo em questão apresenta uma estrutura adequada, conforme a Resolução CFP nº 06/2019. Contudo, na seção "Procedimento", a perita cita a aplicação de instrumentos restritos à(ao) psicóloga(o) (ex: Teste HTP) mas não descreve os resultados de forma clara na seção "Análise", baseando suas conclusões majoritariamente em observações clínicas não sistematizadas. A análise foca excessivamente em descrições comportamentais da genitora, sem a devida articulação com os construtos psicológicos avaliados. A conclusão apresenta afirmações conclusivas sobre a alienação parental que não parecem ser suficientemente sustentadas pelos dados apresentados na análise, carecendo de nexo causal robusto.\n\n4. CONCLUSÃO\nEste parecerista conclui que o laudo psicológico analisado apresenta fragilidades técnico-científicas. Embora a estrutura formal seja adequada, a falta de integração entre os dados dos instrumentos e a análise, bem como as conclusões que extrapolam os resultados apresentados, comprometem a sua consistência. Recomenda-se que o juízo considere a possibilidade de solicitar esclarecimentos à perita ou a realização de uma nova avaliação para subsidiar a decisão de forma mais segura.\n\n5. REFERÊNCIAS\n(Referências sobre avaliação psicológica forense e uso de testes seriam listadas aqui).\n\nBrasília, 11 de setembro de 2025.\n\n___________________________________\nDr. Marcos Vinicius Andrade\nCRP 01/99887`
            }
        };

        setModal({
            isOpen: true,
            type: 'example',
            content: examples[type]?.content || "Exemplo não disponível.",
            title: examples[type]?.title || "Exemplo"
        });
    };

    const closeModal = () => {
        setModal({ isOpen: false, type: 'preview', content: '', title: '' });
    };

    const copyText = () => {
        navigator.clipboard.writeText(modal.content).then(() => {
            alert('Texto copiado!');
        }).catch(err => {
            console.error('Erro ao copiar o texto: ', err);
        });
    };

    return (
        <>
            <style>{`
                @keyframes fadeIn {
                    from { opacity: 0; transform: translateY(10px); }
                    to { opacity: 1; transform: translateY(0); }
                }
                @keyframes modalIn {
                    from { opacity: 0; transform: translateY(-20px) scale(0.95); }
                    to { opacity: 1; transform: translateY(0) scale(1); }
                }
                body {
                    font-family: 'Inter', sans-serif;
                    background-color: #f8fafc;
                    scroll-behavior: smooth;
                }
                .main-content {
                    animation: fadeIn 1s ease-out forwards;
                }
                .accordion-content {
                    max-height: 0;
                    overflow-y: hidden;
                    transition: max-height 0.7s cubic-bezier(0.4, 0, 0.2, 1), padding 0.7s cubic-bezier(0.4, 0, 0.2, 1);
                    padding: 0 1.5rem;
                }
                .accordion-item:hover {
                    transform: translateY(-4px);
                    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.07), 0 4px 6px -4px rgb(0 0 0 / 0.07);
                }
                .accordion-button.open .accordion-icon {
                    transform: rotate(135deg);
                }
                .accordion-icon {
                    transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
                }
                .accordion-button:hover {
                    background-color: #f8fafc;
                }
                .content-fade-in {
                    opacity: 0;
                    transform: translateY(-10px);
                    transition: opacity 0.5s ease 0.2s, transform 0.5s ease 0.2s;
                }
                .accordion-button.open + .accordion-content .content-fade-in {
                    opacity: 1;
                    transform: translateY(0);
                }
                .section-icon { width: 2.5rem; height: 2.5rem; padding: 0.5rem; border-radius: 0.5rem; }
                .form-label {
                    display: block;
                    font-weight: 500;
                    color: #475569;
                    margin-bottom: 0.25rem;
                    font-size: 0.875rem;
                }
                .form-input, .form-textarea {
                    width: 100%;
                    padding: 0.5rem 0.75rem;
                    border: 1px solid #cbd5e1;
                    border-radius: 0.375rem;
                    font-size: 0.875rem;
                    transition: border-color 0.2s, box-shadow 0.2s;
                }
                .form-input:focus, .form-textarea:focus {
                    outline: none;
                    border-color: #4f46e5;
                    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
                }
                .form-textarea {
                    min-height: 100px;
                }
                .tab-button {
                    padding: 0.5rem 1rem;
                    border-bottom: 2px solid transparent;
                    font-weight: 500;
                    color: #64748b;
                    transition: color 0.3s, border-color 0.3s;
                }
                .tab-button:hover {
                    color: #334155;
                }
                .tab-button.active {
                    color: #4f46e5;
                    border-color: #4f46e5;
                    font-weight: 600;
                }
                .tab-panel {
                    display: none;
                    padding-bottom: 1rem;
                }
                .tab-panel.active {
                    display: block;
                    animation: fadeIn 0.5s ease;
                }
                .modal-backdrop {
                    background-color: rgba(15, 23, 42, 0.6);
                    backdrop-filter: blur(4px);
                    transition: opacity 0.3s ease;
                }
                .modal-content {
                    animation: modalIn 0.3s ease-out forwards;
                    background-color: #f1f5f9;
                }
                .modal-preview {
                    background-color: #ffffff;
                    font-family: 'Lora', serif;
                    white-space: pre-wrap;
                    word-wrap: break-word;
                    line-height: 1.6;
                    color: #1e293b;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                }
            `}</style>

            <div className="relative overflow-hidden bg-slate-50 text-slate-800">
                <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-indigo-50 to-slate-50"></div>
                <main className="relative container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16 md:py-24 main-content">
                    <header className="mb-20">
                        <div className="grid md:grid-cols-2 gap-12 items-center">
                            <div className="text-left">
                                <h1 className="text-4xl sm:text-5xl md:text-6xl font-extrabold text-slate-900 tracking-tight text-center md:text-left">
                                    Guia da Resolução CFP nº 06/2019
                                </h1>
                                <p className="mt-6 text-lg text-slate-600">
                                    Um guia interativo e prático para a elaboração de documentos escritos pela(o) psicóloga(o).
                                    Navegue pelas seções, entenda os princípios e gere modelos editáveis para a sua prática profissional.
                                </p>
                            </div>
                            <div className="hidden md:block">
                                <svg width="100%" height="100%" viewBox="0 0 300 300" xmlns="http://www.w3.org/2000/svg">
                                   <g opacity="0.7">
                                        <path d="M 50 50 L 250 50 L 250 250 L 50 250 Z" fill="#eff6ff" stroke="#dbeafe" strokeWidth="2"/>
                                        <rect x="70" y="70" width="160" height="20" fill="#e0e7ff" rx="5"/>
                                        <rect x="70" y="110" width="160" height="10" fill="#e0e7ff" rx="3"/>
                                        <rect x="70" y="130" width="160" height="10" fill="#e0e7ff" rx="3"/>
                                        <rect x="70" y="150" width="120" height="10" fill="#e0e7ff" rx="3"/>
                                        <rect x="70" y="180" width="160" height="10" fill="#e0e7ff" rx="3"/>
                                        <rect x="70" y="200" width="100" height="10" fill="#e0e7ff" rx="3"/>
                                        <path d="M 200 190 L 220 210 L 200 230" fill="none" stroke="#a5b4fc" strokeWidth="3"/>
                                   </g>
                                </svg>
                            </div>
                        </div>
                    </header>

                    <div className="space-y-4">
                        {/* First Accordion - Principles */}
                        <div className="accordion-item bg-white rounded-2xl shadow-md border border-slate-200 overflow-hidden">
                            <button
                                className={`accordion-button w-full flex justify-between items-center p-6 text-left ${openAccordion === 'principles' ? 'open' : ''}`}
                                onClick={() => toggleAccordion('principles')}
                            >
                                <div className="flex items-center gap-4">
                                    <div className="section-icon bg-indigo-100 text-indigo-600">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
                                        </svg>
                                    </div>
                                    <h2 className="text-2xl font-bold text-slate-900">Seção I: Princípios Fundamentais</h2>
                                </div>
                                <div className="accordion-icon text-3xl text-slate-400 font-light">+</div>
                            </button>
                            <div className={`accordion-content ${openAccordion === 'principles' ? 'open' : ''}`} style={{
                                maxHeight: openAccordion === 'principles' ? '1000px' : '0',
                                padding: openAccordion === 'principles' ? '1.5rem' : '0 1.5rem'
                            }}>
                                <div className="border-t border-slate-200 space-y-4 content-fade-in px-6 pt-6 pb-8 text-slate-700">
                                     <p className="mb-6">Esta seção estabelece a base para toda a comunicação escrita. Um documento psicológico não é apenas um papel, mas um instrumento que reflete o raciocínio técnico, a ética profissional e o compromisso científico da(o) psicóloga(o). É a materialização do serviço prestado.</p>
                                     <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div className="p-4 bg-slate-50 rounded-lg border">
                                            <h4 className="font-semibold text-slate-800">Princípios Técnicos</h4>
                                            <p className="text-sm mt-1">O documento deve ser fidedigno, baseado em dados que validam o pensamento psicológico. Deve considerar o contexto histórico-social do indivíduo, a natureza dinâmica do fenômeno psicológico e ser fundamentado em ciência, ética e legislação.</p>
                                        </div>
                                        <div className="p-4 bg-slate-50 rounded-lg border">
                                            <h4 className="font-semibold text-slate-800">Princípios da Linguagem</h4>
                                            <p className="text-sm mt-1">Exige-se linguagem impessoal (3ª pessoa), precisa e com nexo causal. O texto deve seguir a norma culta da língua portuguesa, ser objetivo e garantir os direitos humanos. Descrições literais de atendimentos devem ser evitadas, salvo justificativa técnica clara.</p>
                                        </div>
                                         <div className="p-4 bg-slate-50 rounded-lg border">
                                            <h4 className="font-semibold text-slate-800">Princípios Éticos</h4>
                                            <p className="text-sm mt-1">O sigilo profissional é central e inegociável. O documento deve ser construído para garantir os direitos humanos, evitando sustentar modelos institucionais de segregação. É dever da(o) psicóloga(o) fornecer o documento sempre que solicitado ou ao final de uma avaliação.</p>
                                        </div>
                                     </div>
                                </div>
                            </div>
                        </div>

                        {/* Second Accordion - Document Forms */}
                        <div className="accordion-item bg-white rounded-2xl shadow-md border border-slate-200 overflow-hidden">
                            <button
                                className={`accordion-button w-full flex justify-between items-center p-6 text-left ${openAccordion === 'documents' ? 'open' : ''}`}
                                onClick={() => toggleAccordion('documents')}
                            >
                                 <div className="flex items-center gap-4">
                                    <div className="section-icon bg-blue-100 text-blue-600">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
                                        </svg>
                                    </div>
                                    <h2 className="text-2xl font-bold text-slate-900">Seções II & III: Modalidades e Estruturas</h2>
                                </div>
                                <div className="accordion-icon text-3xl text-slate-400 font-light">+</div>
                            </button>
                            <div className={`accordion-content ${openAccordion === 'documents' ? 'open' : ''}`} style={{
                                maxHeight: openAccordion === 'documents' ? '2000px' : '0',
                                padding: openAccordion === 'documents' ? '1.5rem' : '0 1.5rem'
                            }}>
                                 <div className="py-6 border-t border-slate-200 space-y-8 content-fade-in px-6 pb-8">
                                    <div className="p-4 bg-indigo-50 border-l-4 border-indigo-400 rounded-r-lg space-y-4">
                                        <div>
                                            <h4 className="font-bold text-slate-800 mb-2">Informações do Profissional (para todos os modelos)</h4>
                                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                                <div>
                                                    <label className="form-label">Nome Completo</label>
                                                    <input
                                                        type="text"
                                                        placeholder="Seu nome completo ou social"
                                                        className="form-input"
                                                        value={formData.professional.nome}
                                                        onChange={(e) => handleInputChange('professional', 'nome', e.target.value)}
                                                    />
                                                </div>
                                                <div>
                                                    <label className="form-label">CRP</label>
                                                    <input
                                                        type="text"
                                                        placeholder="XX/XXXXX"
                                                        className="form-input"
                                                        value={formData.professional.crp}
                                                        onChange={(e) => handleInputChange('professional', 'crp', e.target.value)}
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                        <div className="pt-4 border-t border-indigo-200">
                                            <h4 className="font-bold text-slate-800 mb-2">Identidade Visual (Opcional)</h4>
                                            <div className="flex items-center gap-4">
                                                <img src="https://placehold.co/100x100/e0e7ff/4338ca?text=Logo" alt="Preview do Logo" className="w-20 h-20 rounded-md object-contain bg-white border-2 border-dashed border-indigo-300 p-1" />
                                                <div>
                                                    <label className="form-label">Logo da Clínica/Consultório</label>
                                                    <button className="px-4 py-2 text-sm bg-white text-indigo-700 font-semibold rounded-lg border border-indigo-300 hover:bg-indigo-100">Carregar Imagem</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="border-b border-slate-200">
                                        <nav className="flex flex-wrap -mb-px">
                                            <button className={`tab-button ${activeTab === 'declaracao' ? 'active' : ''}`} onClick={() => handleTabClick('declaracao')}>Declaração</button>
                                            <button className={`tab-button ${activeTab === 'atestado' ? 'active' : ''}`} onClick={() => handleTabClick('atestado')}>Atestado</button>
                                            <button className={`tab-button ${activeTab === 'relatorio' ? 'active' : ''}`} onClick={() => handleTabClick('relatorio')}>Relatório</button>
                                            <button className={`tab-button ${activeTab === 'laudo' ? 'active' : ''}`} onClick={() => handleTabClick('laudo')}>Laudo</button>
                                            <button className={`tab-button ${activeTab === 'parecer' ? 'active' : ''}`} onClick={() => handleTabClick('parecer')}>Parecer</button>
                                        </nav>
                                    </div>

                                    <div>
                                        {activeTab === 'declaracao' && (
                                            <div className="tab-panel active">
                                                <h3 className="text-xl font-bold text-slate-800">Declaração</h3>
                                                <p className="text-slate-700 mt-1">Um documento que registra informações objetivas sobre o serviço prestado (ex: comparecimento, horários), sem incluir dados sobre o estado psicológico do atendido.</p>
                                                <div className="mt-4 p-4 bg-slate-100 border border-slate-200 rounded-lg">
                                                    <h5 className="font-semibold text-slate-800">Gerador de Modelo - Declaração</h5>
                                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
                                                        <div>
                                                            <label className="form-label">Nome do Atendido</label>
                                                            <input
                                                                type="text"
                                                                placeholder="Nome completo ou social"
                                                                className="form-input"
                                                                value={formData.declaracao.paciente}
                                                                onChange={(e) => handleInputChange('declaracao', 'paciente', e.target.value)}
                                                            />
                                                        </div>
                                                        <div>
                                                            <label className="form-label">Finalidade</label>
                                                            <input
                                                                type="text"
                                                                placeholder="Ex: Para fins de comprovação de presença"
                                                                className="form-input"
                                                                value={formData.declaracao.finalidade}
                                                                onChange={(e) => handleInputChange('declaracao', 'finalidade', e.target.value)}
                                                            />
                                                        </div>
                                                        <div>
                                                            <label className="form-label">Acompanhante (Opcional)</label>
                                                            <input
                                                                type="text"
                                                                placeholder="Nome do acompanhante"
                                                                className="form-input"
                                                                value={formData.declaracao.acompanhante}
                                                                onChange={(e) => handleInputChange('declaracao', 'acompanhante', e.target.value)}
                                                            />
                                                        </div>
                                                        <div className="sm:col-span-2">
                                                            <label className="form-label">Informações sobre o Serviço</label>
                                                            <textarea
                                                                className="form-textarea"
                                                                placeholder="Ex: Compareceu ao acompanhamento psicológico no dia XX/XX/XXXX, das XX:XX às XX:XX."
                                                                value={formData.declaracao.info}
                                                                onChange={(e) => handleInputChange('declaracao', 'info', e.target.value)}
                                                            />
                                                        </div>
                                                    </div>
                                                    <div className="flex gap-4 mt-4">
                                                        <button onClick={() => previewTemplate('declaracao')} className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">Pré-visualizar Modelo</button>
                                                        <button onClick={() => showExample('declaracao')} className="bg-slate-300 text-slate-800 px-4 py-2 rounded-md hover:bg-slate-400">Ver Exemplo</button>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        {activeTab === 'atestado' && (
                                            <div className="tab-panel active">
                                                <h3 className="text-xl font-bold text-slate-800">Atestado Psicológico</h3>
                                                <p className="text-slate-700 mt-1">Resulta de uma avaliação psicológica e certifica uma determinada situação ou estado psicológico, podendo justificar faltas, aptidão para atividades ou solicitar afastamento.</p>
                                                <div className="mt-4 p-4 bg-slate-100 border border-slate-200 rounded-lg">
                                                    <h5 className="font-semibold text-slate-800">Gerador de Modelo - Atestado</h5>
                                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
                                                        <div>
                                                            <label className="form-label">Nome do Atendido</label>
                                                            <input type="text" placeholder="Nome completo ou social" className="form-input" value={formData.atestado.paciente} onChange={(e) => handleInputChange('atestado', 'paciente', e.target.value)} />
                                                        </div>
                                                        <div>
                                                            <label className="form-label">Nome do Solicitante</label>
                                                            <input type="text" placeholder="Ex: A própria pessoa, Empresa X" className="form-input" value={formData.atestado.solicitante} onChange={(e) => handleInputChange('atestado', 'solicitante', e.target.value)} />
                                                        </div>
                                                        <div className="sm:col-span-2">
                                                            <label className="form-label">Finalidade</label>
                                                            <input type="text" className="form-input" placeholder="Ex: Aptidão para o cargo de..., Justificativa de falta" value={formData.atestado.finalidade} onChange={(e) => handleInputChange('atestado', 'finalidade', e.target.value)} />
                                                        </div>
                                                        <div className="sm:col-span-2">
                                                            <label className="form-label">Descrição das Condições Psicológicas</label>
                                                            <textarea className="form-textarea" placeholder="Atesto que o(a) Sr(a)... apresenta (descrever condições, CID se autorizado)..." value={formData.atestado.condicoes} onChange={(e) => handleInputChange('atestado', 'condicoes', e.target.value)} />
                                                        </div>
                                                    </div>
                                                    <div className="flex gap-4 mt-4">
                                                        <button onClick={() => previewTemplate('atestado')} className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">Pré-visualizar Modelo</button>
                                                        <button onClick={() => showExample('atestado')} className="bg-slate-300 text-slate-800 px-4 py-2 rounded-md hover:bg-slate-400">Ver Exemplo</button>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        {activeTab === 'relatorio' && (
                                            <div className="tab-panel active">
                                                <h3 className="text-xl font-bold text-slate-800">Relatório Psicológico</h3>
                                                <p className="text-slate-700 mt-1">Comunica a atuação profissional em um processo de trabalho, podendo gerar orientações e encaminhamentos, mas **não** tem como finalidade produzir diagnóstico psicológico.</p>
                                                <div className="mt-4 p-4 bg-slate-100 border border-slate-200 rounded-lg">
                                                    <h5 className="font-semibold text-slate-800">Gerador de Estrutura - Relatório</h5>
                                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
                                                         <div><label className="form-label">Interessado</label><input type="text" placeholder="Nome do paciente/grupo/instituição" className="form-input" value={formData.relatorio.interessado} onChange={(e) => handleInputChange('relatorio', 'interessado', e.target.value)} /></div>
                                                        <div><label className="form-label">Solicitante</label><input type="text" placeholder="Nome do solicitante" className="form-input" value={formData.relatorio.solicitante} onChange={(e) => handleInputChange('relatorio', 'solicitante', e.target.value)} /></div>
                                                        <div className="sm:col-span-2"><label className="form-label">Finalidade</label><input type="text" className="form-input" placeholder="Ex: Acompanhamento de processo, Encaminhamento..." value={formData.relatorio.finalidade} onChange={(e) => handleInputChange('relatorio', 'finalidade', e.target.value)} /></div>
                                                        <div className="sm:col-span-2"><label className="form-label">Descrição da Demanda</label><textarea className="form-textarea" placeholder="O que motivou a busca pelo serviço..." value={formData.relatorio.demanda} onChange={(e) => handleInputChange('relatorio', 'demanda', e.target.value)} /></div>
                                                        <div className="sm:col-span-2"><label className="form-label">Procedimento</label><textarea className="form-textarea" placeholder="Raciocínio técnico, recursos utilizados, nº de encontros..." value={formData.relatorio.procedimento} onChange={(e) => handleInputChange('relatorio', 'procedimento', e.target.value)} /></div>
                                                        <div className="sm:col-span-2"><label className="form-label">Análise</label><textarea className="form-textarea" placeholder="Exposição descritiva e analítica do trabalho realizado..." value={formData.relatorio.analise} onChange={(e) => handleInputChange('relatorio', 'analise', e.target.value)} /></div>
                                                        <div className="sm:col-span-2"><label className="form-label">Conclusão</label><textarea className="form-textarea" placeholder="Conclusões a partir da análise, encaminhamentos, orientações..." value={formData.relatorio.conclusao} onChange={(e) => handleInputChange('relatorio', 'conclusao', e.target.value)} /></div>
                                                    </div>
                                                    <div className="flex gap-4 mt-4">
                                                        <button onClick={() => previewTemplate('relatorio')} className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">Pré-visualizar Estrutura</button>
                                                        <button onClick={() => showExample('relatorio')} className="bg-slate-300 text-slate-800 px-4 py-2 rounded-md hover:bg-slate-400">Ver Exemplo</button>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        {activeTab === 'laudo' && (
                                            <div className="tab-panel active">
                                                <h3 className="text-xl font-bold text-slate-800">Laudo Psicológico</h3>
                                                <p className="text-slate-700 mt-1">É o resultado de um processo de avaliação psicológica, com finalidade de subsidiar decisões. Apresenta informações técnicas e científicas, e a citação de referências é obrigatória.</p>
                                                <div className="mt-4 p-4 bg-slate-100 border border-slate-200 rounded-lg">
                                                    <h5 className="font-semibold text-slate-800">Gerador de Estrutura - Laudo</h5>
                                                     <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
                                                        <div><label className="form-label">Interessado</label><input type="text" placeholder="Nome do paciente/grupo/instituição" className="form-input" value={formData.laudo.interessado} onChange={(e) => handleInputChange('laudo', 'interessado', e.target.value)} /></div>
                                                        <div><label className="form-label">Solicitante</label><input type="text" placeholder="Nome do solicitante" className="form-input" value={formData.laudo.solicitante} onChange={(e) => handleInputChange('laudo', 'solicitante', e.target.value)} /></div>
                                                        <div className="sm:col-span-2"><label className="form-label">Finalidade</label><input type="text" className="form-input" placeholder="Ex: Subsidiar decisão judicial, Avaliação para cirurgia..." value={formData.laudo.finalidade} onChange={(e) => handleInputChange('laudo', 'finalidade', e.target.value)} /></div>
                                                        <div className="sm:col-span-2"><label className="form-label">Descrição da Demanda</label><textarea className="form-textarea" placeholder="O que motivou a busca pela avaliação psicológica..." value={formData.laudo.demanda} onChange={(e) => handleInputChange('laudo', 'demanda', e.target.value)} /></div>
                                                        <div className="sm:col-span-2"><label className="form-label">Procedimento</label><textarea className="form-textarea" placeholder="Instrumentos, testes, entrevistas e outras fontes utilizadas..." value={formData.laudo.procedimento} onChange={(e) => handleInputChange('laudo', 'procedimento', e.target.value)} /></div>
                                                        <div className="sm:col-span-2"><label className="form-label">Análise</label><textarea className="form-textarea" placeholder="Análise metódica e objetiva dos dados colhidos e sua integração..." value={formData.laudo.analise} onChange={(e) => handleInputChange('laudo', 'analise', e.target.value)} /></div>
                                                        <div className="sm:col-span-2"><label className="form-label">Conclusão</label><textarea className="form-textarea" placeholder="Conclusões, diagnóstico/hipótese, prognóstico, encaminhamentos..." value={formData.laudo.conclusao} onChange={(e) => handleInputChange('laudo', 'conclusao', e.target.value)} /></div>
                                                    </div>
                                                    <div className="flex gap-4 mt-4">
                                                        <button onClick={() => previewTemplate('laudo')} className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">Pré-visualizar Estrutura</button>
                                                        <button onClick={() => showExample('laudo')} className="bg-slate-300 text-slate-800 px-4 py-2 rounded-md hover:bg-slate-400">Ver Exemplo</button>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        {activeTab === 'parecer' && (
                                            <div className="tab-panel active">
                                                <h3 className="text-xl font-bold text-slate-800">Parecer Psicológico</h3>
                                                <p className="text-slate-700 mt-1">É um pronunciamento por escrito que responde a uma consulta sobre uma questão-problema ou a um documento psicológico questionado. Não é resultado de uma avaliação ou intervenção direta.</p>
                                                <div className="mt-4 p-4 bg-slate-100 border border-slate-200 rounded-lg">
                                                    <h5 className="font-semibold text-slate-800">Gerador de Estrutura - Parecer</h5>
                                                     <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
                                                        <div><label className="form-label">Solicitante</label><input type="text" placeholder="Nome do solicitante" className="form-input" value={formData.parecer.solicitante} onChange={(e) => handleInputChange('parecer', 'solicitante', e.target.value)} /></div>
                                                        <div><label className="form-label">Assunto</label><input type="text" placeholder="Objeto da consulta" className="form-input" value={formData.parecer.assunto} onChange={(e) => handleInputChange('parecer', 'assunto', e.target.value)} /></div>
                                                        <div className="sm:col-span-2"><label className="form-label">Descrição da Demanda</label><textarea className="form-textarea" placeholder="Transcrição do objetivo da consulta..." value={formData.parecer.demanda} onChange={(e) => handleInputChange('parecer', 'demanda', e.target.value)} /></div>
                                                        <div className="sm:col-span-2"><label className="form-label">Análise</label><textarea className="form-textarea" placeholder="Análise minuciosa da questão com base na teoria e técnica..." value={formData.parecer.analise} onChange={(e) => handleInputChange('parecer', 'analise', e.target.value)} /></div>
                                                        <div className="sm:col-span-2"><label className="form-label">Conclusão</label><textarea className="form-textarea" placeholder="Posicionamento do parecerista sobre a questão..." value={formData.parecer.conclusao} onChange={(e) => handleInputChange('parecer', 'conclusao', e.target.value)} /></div>
                                                    </div>
                                                    <div className="flex gap-4 mt-4">
                                                        <button onClick={() => previewTemplate('parecer')} className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">Pré-visualizar Estrutura</button>
                                                        <button onClick={() => showExample('parecer')} className="bg-slate-300 text-slate-800 px-4 py-2 rounded-md hover:bg-slate-400">Ver Exemplo</button>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                     </div>
                                 </div>
                            )}
                        </div>

                        {/* Third Accordion - Final Procedures */}
                        <div className="accordion-item bg-white rounded-2xl shadow-md border border-slate-200 overflow-hidden">
                            <button
                                className={`accordion-button w-full flex justify-between items-center p-6 text-left ${openAccordion === 'procedures' ? 'open' : ''}`}
                                onClick={() => toggleAccordion('procedures')}
                            >
                                <div className="flex items-center gap-4">
                                    <div className="section-icon bg-green-100 text-green-600">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <h2 className="text-2xl font-bold text-slate-900">Seções IV a VII: Procedimentos Finais</h2>
                                </div>
                                <div className="accordion-icon text-3xl text-slate-400 font-light">+</div>
                            </button>
                            <div className={`accordion-content ${openAccordion === 'procedures' ? 'open' : ''}`} style={{
                                maxHeight: openAccordion === 'procedures' ? '1000px' : '0',
                                padding: openAccordion === 'procedures' ? '1.5rem' : '0 1.5rem'
                            }}>
                                <div className="border-t border-slate-200 space-y-4 content-fade-in px-6 pt-6 pb-8 text-slate-700">
                                    <p className="mb-6">Esta seção aborda os procedimentos finais para a elaboração e entrega dos documentos psicológicos, incluindo guarda, prazo de validade, entrega e orientações sobre assinatura digital.</p>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div className="p-4 bg-slate-50 rounded-lg border">
                                            <h4 className="font-semibold text-slate-800">Guarda e Arquivo</h4>
                                            <p className="text-sm mt-1">Os documentos devem ser arquivados pelo prazo mínimo de 5 anos, podendo ser mantidos por período superior a critério da(o) psicóloga(o). A guarda deve garantir sigilo e integridade.</p>
                                        </div>
                                        <div className="p-4 bg-slate-50 rounded-lg border">
                                            <h4 className="font-semibold text-slate-800">Prazo de Validade</h4>
                                            <p className="text-sm mt-1">Não há prazo de validade fixo. O documento é válido enquanto as informações nele contidas mantiverem sua pertinência e utilidade para a finalidade a que se destina.</p>
                                        </div>
                                        <div className="p-4 bg-slate-50 rounded-lg border">
                                            <h4 className="font-semibold text-slate-800">Entrega</h4>
                                            <p className="text-sm mt-1">O documento deve ser entregue pessoalmente ao interessado ou seu representante legal. Em caso de menor de idade, aos pais ou responsáveis, salvo situações específicas previstas em lei.</p>
                                        </div>
                                        <div className="p-4 bg-slate-50 rounded-lg border">
                                            <h4 className="font-semibold text-slate-800">Assinatura Digital</h4>
                                            <p className="text-sm mt-1">É permitida a assinatura digital com certificado ICP-Brasil. Para documentos oficiais, recomenda-se o uso da plataforma Gov.br para garantir autenticidade e validade jurídica.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Modals */}
                    {modal.isOpen && (
                        <div className="fixed inset-0 z-50 flex items-center justify-center modal-backdrop" onClick={() => setModal({ isOpen: false, type: 'preview', title: '', content: '' })}>
                            <div className="modal-content w-full max-w-4xl max-h-[90vh] mx-4 rounded-2xl overflow-hidden" onClick={(e) => e.stopPropagation()}>
                                <div className="flex justify-between items-center p-6 border-b border-slate-300">
                                    <h3 className="text-xl font-bold text-slate-800">{modal.title}</h3>
                                    <button onClick={() => setModal({ isOpen: false, type: 'preview', title: '', content: '' })} className="text-slate-500 hover:text-slate-700 text-2xl">&times;</button>
                                </div>
                                <div className="p-6 overflow-y-auto max-h-[70vh]">
                                    <div className="modal-preview p-6 rounded-lg text-sm leading-relaxed">{modal.content}</div>
                                </div>
                                <div className="flex justify-end gap-4 p-6 border-t border-slate-300">
                                    <button onClick={() => copyText(modal.content)} className="bg-slate-200 text-slate-800 px-4 py-2 rounded-md hover:bg-slate-300">Copiar Texto</button>
                                    <button onClick={() => setModal({ isOpen: false, type: 'preview', title: '', content: '' })} className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">Fechar</button>
                                </div>
                            </div>
                        </div>
                    )}

                    <footer className="mt-20 pt-8 border-t border-slate-200 text-center text-slate-600">
                        <p className="text-sm">
                            Baseado na <strong>Resolução CFP nº 06/2019</strong> - Institui regras para a elaboração de documentos escritos produzidos pela(o) psicóloga(o) no exercício profissional.
                        </p>
                        <p className="text-xs mt-2 text-slate-500">
                            Esta ferramenta é um guia de apoio. Consulte sempre a resolução original e busque supervisão quando necessário.
                        </p>
                    </footer>
                </main>
            </div>
        </>
    );
}
