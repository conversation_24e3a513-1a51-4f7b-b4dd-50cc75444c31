import { NextResponse } from 'next/server';
import crypto from 'crypto';

// Um armazenamento em memória para simular um banco de dados.
// Em um aplicativo real, você usaria um banco de dados (ex: Redis, PostgreSQL)
// para associar o hash do documento ao 'state' da sessão do usuário.
const signatureStore = new Map();

export async function POST(request) {
  try {
    const formData = await request.formData();
    const file = formData.get('file');

    if (!file) {
      return NextResponse.json({ error: 'Nenhum arquivo enviado.' }, { status: 400 });
    }

    // 1. Calcular o hash SHA-256 do arquivo PDF
    const fileBuffer = Buffer.from(await file.arrayBuffer());
    const hash = crypto.createHash('sha256').update(fileBuffer).digest('hex');

    // 2. Gerar um 'state' único para segurança (prevenção de CSRF)
    const state = crypto.randomBytes(16).toString('hex');
    
    // 3. Salvar o hash e associá-lo ao 'state' (simulação de DB)
    signatureStore.set(state, { docHash: hash, timestamp: Date.now() });
    console.log(`[Backend] Hash do documento calculado e armazenado para o state: ${state}`);

    // 4. Construir a URL de autorização do gov.br
    const redirectUri = `${process.env.NEXT_PUBLIC_APP_URL}/api/govbr/callback`;
    
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: process.env.GOVBR_CLIENT_ID,
      scope: 'assinatura_eletronica', // Escopo necessário
      redirect_uri: redirectUri,
      state: state,
    });

    const authorizationUrl = `https://sso.acesso.gov.br/authorize?${params.toString()}`;

    // 5. Retornar a URL para o frontend redirecionar o usuário
    return NextResponse.json({ authorizationUrl });

  } catch (error) {
    console.error('[API Initiate Error]', error);
    return NextResponse.json({ error: 'Erro ao iniciar o processo de assinatura.' }, { status: 500 });
  }
}
